import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:learning_english_smart/screens/ai_chat_screen/model/chat_message.dart';
import 'package:learning_english_smart/screens/saved_words_screen/model/saved_word.dart';
import '../pages/Auth/sign_in/sign_in_screen.dart';
import '../pages/application/application_page.dart';
import '../pages/Home_Page/home_page.dart';
import '../screens/ranker_screen/ranker_screen.dart';
import '../pages/Account_Page/account_page.dart';
import '../pages/profile_page.dart';
import '../pages/settings_page.dart';
import '../pages/Learn_Page/learn_page.dart';
import '../screens/course_screen/course_screen.dart';
import '../screens/my_book_screen/my_book_screen.dart';
import '../screens/saved_words_screen/saved_words_screen.dart';
import '../screens/vocabulary_detail_screen/vocabulary_detail_screen.dart';
import '../screens/vocabulary_screen/vocabulary_screen.dart';
import '../screens/ai_chat_screen/ai_chat_screen.dart';
import '../screens/exercise_screen/exercise_screen.dart';
import '../screens/conversation_practice_screen/conversation_practice_screen.dart';
import '../pages/BanBe_Page/BanBe_Page.dart';
import '../screens/speaking_practice_screen/speaking_practice_screen.dart';
part 'app_router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType => RouteType.material();

  @override
  List<AutoRoute> get routes => [
        // Sign In route (Initial route)
        AutoRoute(
          page: SignInRoute.page,
          initial: true,
        ),

        // Application route (Main app with bottom navigation)
        AutoRoute(
          page: ApplicationRoute.page,
          path: '/app',
        ),

        // Home route
        AutoRoute(
          page: HomeRoute.page,
        ),

        // Learn route
        AutoRoute(
          page: LearnRoute.page,
          path: '/learn',
        ),

        // // Friends route
        // AutoRoute(
        //   page: BanBeRoute.page,
        // ),

        // Account route
        AutoRoute(
          page: AccountRoute.page,
        ),

        // Profile route
        AutoRoute(
          page: ProfileRoute.page,
        ),

        // Settings route
        AutoRoute(
          page: SettingsRoute.page,
          path: '/settings',
        ),

        // Course route
        AutoRoute(
          page: CourseRoute.page,
          path: '/course',
        ),

        // My Book route
        AutoRoute(
          page: MyBookRoute.page,
          path: '/my-book',
        ),

        // Saved Words route
        AutoRoute(
          page: SavedWordsRoute.page,
          path: '/saved-words',
        ),

        // Vocabulary Detail route
        AutoRoute(
          page: VocabularyDetailRoute.page,
          path: '/vocabulary-detail',
        ),

        // Vocabulary route
        AutoRoute(
          page: VocabularyRoute.page,
          path: '/vocabulary',
        ),

        // AI Chat route
        AutoRoute(
          page: AiChatRoute.page,
          path: '/ai-chat',
        ),

        // Exercise route
        AutoRoute(
          page: ExerciseRoute.page,
          path: '/exercise',
        ),

        // Conversation Practice route
        AutoRoute(
          page: ConversationPracticeRoute.page,
          path: '/conversation-practice',
        ),

        // Practice Partners route
        AutoRoute(
          page: BanBeRoute.page,
          path: '/practice-partners',
        ),

        // Speaking Practice route
        AutoRoute(
          page: SpeakingPracticeRoute.page,
          path: '/speaking-practice',
        ),

        AutoRoute(
          page: Ranker_Route.page,
          path: '/speaking-practice',
        ),
      ];
}
