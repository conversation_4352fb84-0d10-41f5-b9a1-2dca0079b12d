enum ExerciseType {
  study,
  multipleChoice,
  speaking,
  translation,
  listening,
  wordOrder,
  completion
}

class ExerciseItem {
  final ExerciseType type;
  final String question;
  final String? audioUrl;
  final List<String> options;
  final String correctAnswer;
  final String? translation;

  ExerciseItem({
    required this.type,
    required this.question,
    this.audioUrl,
    required this.options,
    required this.correctAnswer,
    this.translation,
  });
}

// Model cho ví dụ từ vựng
class VocabularyExample {
  final String english;
  final String vietnamese;

  VocabularyExample({
    required this.english,
    required this.vietnamese,
  });

  factory VocabularyExample.fromJson(Map<String, dynamic> json) {
    return VocabularyExample(
      english: json['english'] ?? '',
      vietnamese: json['vietnamese'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'english': english,
      'vietnamese': vietnamese,
    };
  }
}

// Model chính cho từ vựng từ JSON
class VocabularyWord {
  final String word;
  final String partOfSpeech;
  final String level;
  final String vietnameseMeaning;
  final List<VocabularyExample> examples;
  final List<String> antonyms;

  VocabularyWord({
    required this.word,
    required this.partOfSpeech,
    required this.level,
    required this.vietnameseMeaning,
    required this.examples,
    required this.antonyms,
  });

  factory VocabularyWord.fromJson(Map<String, dynamic> json) {
    return VocabularyWord(
      word: json['word'] ?? '',
      partOfSpeech: json['part_of_speech'] ?? json['partOfSpeech'] ?? '',
      level: json['level'] ?? 'A1',
      vietnameseMeaning:
          json['vietnamese_meaning'] ?? json['vietnameseMeaning'] ?? '',
      examples: (json['examples'] as List<dynamic>?)
              ?.map(
                  (e) => VocabularyExample.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      antonyms: (json['antonyms'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'word': word,
      'part_of_speech': partOfSpeech,
      'level': level,
      'vietnamese_meaning': vietnameseMeaning,
      'examples': examples.map((e) => e.toJson()).toList(),
      'antonyms': antonyms,
    };
  }

  // Chuyển đổi sang VocabularyItem để tương thích với UI hiện tại
  VocabularyItem toVocabularyItem() {
    return VocabularyItem(
      word: word,
      pronunciation: '', // Sẽ được thêm sau
      meaning: vietnameseMeaning,
      example: examples.isNotEmpty
          ? '${examples.first.english}\n(${examples.first.vietnamese})'
          : '',
      imageUrl: '', // Sẽ được thêm sau
      choices: _generateChoices(),
      exercises: [],
    );
  }

  // Tạo các lựa chọn cho bài tập multiple choice
  List<String> _generateChoices() {
    // Tạm thời trả về danh sách mặc định
    // Sau này có thể cải thiện bằng cách tạo các lựa chọn sai từ database
    return [
      vietnameseMeaning,
      'Lựa chọn sai 1',
      'Lựa chọn sai 2',
      'Lựa chọn sai 3',
    ];
  }
}

// Model cũ để tương thích với UI hiện tại
class VocabularyItem {
  final String word;
  final String pronunciation;
  final String meaning;
  final String example;
  final String imageUrl;
  final List<String> choices;
  final List<ExerciseItem> exercises;

  VocabularyItem({
    required this.word,
    required this.pronunciation,
    required this.meaning,
    required this.example,
    required this.imageUrl,
    required this.choices,
    required this.exercises,
  });
}
