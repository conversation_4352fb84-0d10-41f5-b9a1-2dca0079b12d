import '../models/user_model.dart';

class AuthResult {
  final bool success;
  final UserModel? user;
  final String? token;
  final String? error;

  AuthResult({
    required this.success,
    this.user,
    this.token,
    this.error,
  });
}

class AuthService {
  // Simulate API calls with delays
  Future<AuthResult> signInWithEmail(String email, String password) async {
    await Future.delayed(const Duration(seconds: 2));
    
    // Simulate validation
    if (email.isEmpty || password.isEmpty) {
      return AuthResult(success: false, error: 'Email and password are required');
    }
    
    if (password.length < 6) {
      return AuthResult(success: false, error: 'Password must be at least 6 characters');
    }
    
    // Simulate successful login
    final user = UserModel(
      id: '1',
      email: email,
      fullName: 'John Doe',
      createdAt: DateTime.now(),
      isEmailVerified: true,
    );
    
    return AuthResult(
      success: true,
      user: user,
      token: 'mock_token_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  Future<AuthResult> signUpWithEmail(String email, String password, String fullName) async {
    await Future.delayed(const Duration(seconds: 2));
    
    // Simulate validation
    if (email.isEmpty || password.isEmpty || fullName.isEmpty) {
      return AuthResult(success: false, error: 'All fields are required');
    }
    
    if (password.length < 6) {
      return AuthResult(success: false, error: 'Password must be at least 6 characters');
    }
    
    if (!email.contains('@')) {
      return AuthResult(success: false, error: 'Please enter a valid email');
    }
    
    // Simulate successful registration
    final user = UserModel(
      id: '1',
      email: email,
      fullName: fullName,
      createdAt: DateTime.now(),
      isEmailVerified: false,
    );
    
    return AuthResult(
      success: true,
      user: user,
      token: 'mock_token_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  Future<AuthResult> signInWithGoogle() async {
    await Future.delayed(const Duration(seconds: 2));
    
    // Simulate Google sign in
    final user = UserModel(
      id: '2',
      email: '<EMAIL>',
      fullName: 'Google User',
      createdAt: DateTime.now(),
      isEmailVerified: true,
    );
    
    return AuthResult(
      success: true,
      user: user,
      token: 'google_token_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  Future<AuthResult> signInWithApple() async {
    await Future.delayed(const Duration(seconds: 2));
    
    // Simulate Apple sign in
    final user = UserModel(
      id: '3',
      email: '<EMAIL>',
      fullName: 'Apple User',
      createdAt: DateTime.now(),
      isEmailVerified: true,
    );
    
    return AuthResult(
      success: true,
      user: user,
      token: 'apple_token_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  Future<AuthResult> sendPhoneVerification(String phoneNumber) async {
    await Future.delayed(const Duration(seconds: 1));
    
    if (phoneNumber.isEmpty) {
      return AuthResult(success: false, error: 'Phone number is required');
    }
    
    // Simulate sending SMS
    return AuthResult(success: true);
  }

  Future<AuthResult> verifyPhoneCode(String phoneNumber, String code) async {
    await Future.delayed(const Duration(seconds: 2));
    
    if (code.length != 6) {
      return AuthResult(success: false, error: 'Please enter a valid 6-digit code');
    }
    
    // Simulate successful verification
    final user = UserModel(
      id: '4',
      email: '',
      fullName: 'Phone User',
      phoneNumber: phoneNumber,
      createdAt: DateTime.now(),
      isPhoneVerified: true,
    );
    
    return AuthResult(
      success: true,
      user: user,
      token: 'phone_token_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  Future<AuthResult> resetPassword(String email) async {
    await Future.delayed(const Duration(seconds: 1));
    
    if (email.isEmpty || !email.contains('@')) {
      return AuthResult(success: false, error: 'Please enter a valid email');
    }
    
    // Simulate sending reset email
    return AuthResult(success: true);
  }

  Future<void> signOut() async {
    await Future.delayed(const Duration(milliseconds: 500));
    // Simulate sign out
  }
}
