import 'package:flutter/material.dart';

import '../core/app_export.dart';
import './custom_image_view.dart';

/**
 * CustomAppBar - A flexible and reusable AppBar component
 * 
 * This component provides a customizable app bar that can handle various layouts
 * including back buttons, settings buttons, and centered titles. It implements
 * PreferredSizeWidget to work seamlessly with <PERSON><PERSON><PERSON>.
 * 
 * @param title - The title text to display in the app bar
 * @param showBackButton - Whether to show the back button on the left
 * @param showSettingsButton - Whether to show the settings button on the right
 * @param onBackPressed - Callback function when back button is pressed
 * @param onSettingsPressed - Callback function when settings button is pressed
 * @param backgroundColor - Background color of the app bar
 * @param titleStyle - Custom text style for the title
 * @param height - Custom height for the app bar
 * @param centerTitle - Whether to center the title
 * @param leadingIcon - Custom leading icon path
 * @param trailingIcon - Custom trailing icon path
 * @param elevation - Elevation of the app bar
 */
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({
    Key? key,
    this.title,
    this.showBackButton,
    this.showSettingsButton,
    this.onBackPressed,
    this.onSettingsPressed,
    this.backgroundColor,
    this.titleStyle,
    this.height,
    this.centerTitle,
    this.leadingIcon,
    this.trailingIcon,
    this.elevation,
  }) : super(key: key);

  /// The title text to display in the app bar
  final String? title;

  /// Whether to show the back button on the left
  final bool? showBackButton;

  /// Whether to show the settings button on the right
  final bool? showSettingsButton;

  /// Callback function when back button is pressed
  final VoidCallback? onBackPressed;

  /// Callback function when settings button is pressed
  final VoidCallback? onSettingsPressed;

  /// Background color of the app bar
  final Color? backgroundColor;

  /// Custom text style for the title
  final TextStyle? titleStyle;

  /// Custom height for the app bar
  final double? height;

  /// Whether to center the title
  final bool? centerTitle;

  /// Custom leading icon path
  final String? leadingIcon;

  /// Custom trailing icon path
  final String? trailingIcon;

  /// Elevation of the app bar
  final double? elevation;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: backgroundColor ?? appTheme.whiteCustom,
      elevation: elevation ?? 0,
      toolbarHeight: height ?? 72.h,
      centerTitle: centerTitle ?? false,
      automaticallyImplyLeading: false,
      leading: _buildLeading(context), // Modified: Added context parameter
      title: _buildTitle(),
      actions: _buildActions(),
    );
  }

  Widget? _buildLeading(BuildContext context) {
    // Modified: Added BuildContext parameter
    if (showBackButton == true) {
      return IconButton(
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
        icon: CustomImageView(
          imagePath: leadingIcon ?? ImageConstant.imgDepth3Frame0,
          height: 24.h,
          width: 24.h,
        ),
        iconSize: 48.h,
      );
    }
    return null;
  }

  Widget? _buildTitle() {
    if (title != null) {
      return Container(
        alignment:
            centerTitle == true ? Alignment.center : Alignment.centerLeft,
        child: Text(
          title!,
          style: titleStyle ??
              TextStyleHelper.instance.title18Bold.copyWith(height: 1.28),
        ),
      );
    }
    return null;
  }

  List<Widget>? _buildActions() {
    if (showSettingsButton == true) {
      return [
        IconButton(
          onPressed: onSettingsPressed,
          icon: CustomImageView(
            imagePath: trailingIcon ?? ImageConstant.imgDepth3Frame1,
            height: 48.h,
            width: 48.h,
          ),
          iconSize: 48.h,
        ),
      ];
    }
    return null;
  }

  @override
  Size get preferredSize => Size.fromHeight(height ?? 72.h);
}
