import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:learning_english_smart/core/app_export.dart';
import 'firebase_options.dart'; // Firebase configuration
import 'services/vocabulary_cache_service.dart';
import 'package:learning_english_smart/pages/Home_Page/provider/home_provider.dart';
import 'package:learning_english_smart/pages/Auth/sign_in/prv/auth_provider.dart';
import 'package:learning_english_smart/pages/Learn_Page/provider/learn_provider.dart';
import 'package:learning_english_smart/pages/application/provider/application_provider.dart';
import 'package:learning_english_smart/screens/my_book_screen/prv/my_book_provider.dart';
import 'package:learning_english_smart/screens/vocabulary_screen/prv/vocabulary_provider.dart';
import 'package:learning_english_smart/screens/ai_chat_screen/prv/chat_provider.dart';
import 'package:learning_english_smart/screens/conversation_practice_screen/prv/conversation_provider.dart';
import 'package:learning_english_smart/pages/BanBe_Page/prv/BanBe_provider.dart';
import 'routes/app_router.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialized successfully');

    // Preload vocabulary cache
    print('🔄 Preloading vocabulary cache...');
    final cacheService = VocabularyCacheService();
    await cacheService.initialize();
    print('✅ Vocabulary cache preloaded');
  } catch (e) {
    print('❌ Firebase initialization error: $e');
    // Continue without Firebase for now
  }

  runApp(MultiProvider(providers: [
    ChangeNotifierProvider(create: (context) => AuthProvider()),
    ChangeNotifierProvider(create: (context) => LearnPrv()),
    ChangeNotifierProvider(create: (context) => HomePrv()),
    ChangeNotifierProvider(create: (context) => ApplicationProvider()),
    ChangeNotifierProvider(create: (context) => MyBookProvider()),
    ChangeNotifierProvider(create: (context) => VocabularyProvider()),
    ChangeNotifierProvider(create: (context) => ChatProvider()),
    ChangeNotifierProvider(create: (context) => ConversationProvider()),
    ChangeNotifierProvider(create: (context) => BanBeProvider()),
  ], child: MyApp()));
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  final _appRouter = AppRouter();

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp.router(
              title: 'Learning English Smart',
              theme: ThemeData(
                colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
                useMaterial3: true,
              ),
              routerConfig: _appRouter.config(),
              debugShowCheckedModeBanner: false,
              localizationsDelegates: [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: [
                const Locale('en', 'US'),
                const Locale('vi', 'VN'),
              ],
            );
          },
        );
      },
    );
  }
}
