import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:learning_english_smart/pages/Home_Page/provider/home_provider.dart';
import 'package:learning_english_smart/pages/Home_Page/widgets/home_card_widget.dart';
import 'package:learning_english_smart/routes/app_router.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_app_bar.dart';
import '../../../widgets/custom_bottom_bar.dart';
import '../../../widgets/custom_image_view.dart';
import '../../../widgets/custom_bottom_navigation_bar.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  static Widget builder(BuildContext context) {
    return ChangeNotifierProvider<HomePrv>(
      // Modified: Fixed typo in provider class name
      create: (context) => HomePrv(),
      child: const HomePage(),
    );
  }

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    // Dữ liệu đã được khởi tạo trong constructor của HomePrv
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: appTheme.whiteCustom,
      appBar: CustomAppBar(
        title: 'Learn',
        showSettingsButton: true,
        centerTitle: false,
        onSettingsPressed: () {
          Provider.of<HomePrv>(context, listen: false).onSettingsTap();
        },
      ),
      body: Consumer<HomePrv>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDailyGoalSection(provider),
                _buildProgressBar(provider),
                _buildContinueLearningSection(provider),
                _buildPracticeSection(provider),
                _buildStoriesSection(provider),
                SizedBox(height: 20.h),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildDailyGoalSection(HomePrv provider) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 16.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Daily goal',
            style: TextStyleHelper.instance.title16Medium,
          ),
          Text(
            '${provider.languageLearningDashboardModel.currentXP ?? 60}/${provider.languageLearningDashboardModel.targetXP ?? 100} XP',
            style: TextStyleHelper.instance.body14Regular
                .copyWith(color: appTheme.colorFF1116),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar(HomePrv provider) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      child: Container(
        height: 8.h,
        decoration: BoxDecoration(
          color: appTheme.colorFFDBE2,
          borderRadius: BorderRadius.circular(4.h),
        ),
        child: Stack(
          children: [
            Container(
              width: 215.h,
              height: 8.h,
              decoration: BoxDecoration(
                color: appTheme.colorFF1116,
                borderRadius: BorderRadius.circular(4.h),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContinueLearningSection(HomePrv provider) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 44.h),
          Text(
            'Continue learning',
            style: TextStyleHelper.instance.title22Bold,
          ),
          SizedBox(height: 28.h),
          SizedBox(
            height: 220.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: provider.languageCards.length,
              separatorBuilder: (context, index) => SizedBox(width: 16.h),
              itemBuilder: (context, index) {
                return HomeCardWidget(
                  languageCard: provider.languageCards[index],
                  onTap: () {
                    provider.onLanguageCardTap(provider.languageCards[index]);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPracticeSection(HomePrv provider) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 32.h),
          Text(
            'Practice',
            style: TextStyleHelper.instance.title22Bold,
          ),
          SizedBox(height: 28.h),
          GestureDetector(
            onTap: () {
              provider.onPracticeTap();
            },
            child: Container(
              height: 92.h,
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'New',
                          style: TextStyleHelper.instance.body14Regular,
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          'Practice',
                          style: TextStyleHelper.instance.title16Bold,
                        ),
                        SizedBox(height: 8.h),
                        SizedBox(
                          width: 188.h,
                          child: Text(
                            'Practice your new words and phrases',
                            style: TextStyleHelper.instance.body14Regular
                                .copyWith(height: 1.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16.h),
                  CustomImageView(
                    imagePath: ImageConstant.imgDepth4Frame1,
                    height: 91.h,
                    width: 130.h,
                    fit: BoxFit.cover,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStoriesSection(HomePrv provider) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 32.h),
          GestureDetector(
            onTap: () {
              provider.onStoriesTap();
            },
            child: Container(
              height: 68.h,
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Stories',
                          style: TextStyleHelper.instance.title16Bold,
                        ),
                        SizedBox(height: 8.h),
                        SizedBox(
                          width: 207.h,
                          child: Text(
                            'Read short stories in your target language',
                            style: TextStyleHelper.instance.body14Regular
                                .copyWith(height: 1.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16.h),
                  CustomImageView(
                    imagePath: ImageConstant.imgDepth4Frame166x130,
                    height: 66.h,
                    width: 130.h,
                    fit: BoxFit.cover,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
