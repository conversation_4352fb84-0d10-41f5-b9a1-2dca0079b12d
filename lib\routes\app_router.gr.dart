// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [AccountPage]
class AccountRoute extends PageRouteInfo<void> {
  const AccountRoute({List<PageRouteInfo>? children})
    : super(AccountRoute.name, initialChildren: children);

  static const String name = 'AccountRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const AccountPage();
    },
  );
}

/// generated route for
/// [AiChatScreen]
class AiChatRoute extends PageRouteInfo<void> {
  const AiChatRoute({List<PageRouteInfo>? children})
    : super(AiChatRoute.name, initialChildren: children);

  static const String name = 'AiChatRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const AiChatScreen();
    },
  );
}

/// generated route for
/// [ApplicationPage]
class ApplicationRoute extends PageRouteInfo<void> {
  const ApplicationRoute({List<PageRouteInfo>? children})
    : super(ApplicationRoute.name, initialChildren: children);

  static const String name = 'ApplicationRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ApplicationPage();
    },
  );
}

/// generated route for
/// [BanBePage]
class BanBeRoute extends PageRouteInfo<void> {
  const BanBeRoute({List<PageRouteInfo>? children})
    : super(BanBeRoute.name, initialChildren: children);

  static const String name = 'BanBeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const BanBePage();
    },
  );
}

/// generated route for
/// [ConversationPracticeScreen]
class ConversationPracticeRoute
    extends PageRouteInfo<ConversationPracticeRouteArgs> {
  ConversationPracticeRoute({
    Key? key,
    required PracticeUser partner,
    List<PageRouteInfo>? children,
  }) : super(
         ConversationPracticeRoute.name,
         args: ConversationPracticeRouteArgs(key: key, partner: partner),
         initialChildren: children,
       );

  static const String name = 'ConversationPracticeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ConversationPracticeRouteArgs>();
      return ConversationPracticeScreen(key: args.key, partner: args.partner);
    },
  );
}

class ConversationPracticeRouteArgs {
  const ConversationPracticeRouteArgs({this.key, required this.partner});

  final Key? key;

  final PracticeUser partner;

  @override
  String toString() {
    return 'ConversationPracticeRouteArgs{key: $key, partner: $partner}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ConversationPracticeRouteArgs) return false;
    return key == other.key && partner == other.partner;
  }

  @override
  int get hashCode => key.hashCode ^ partner.hashCode;
}

/// generated route for
/// [CourseScreen]
class CourseRoute extends PageRouteInfo<void> {
  const CourseRoute({List<PageRouteInfo>? children})
    : super(CourseRoute.name, initialChildren: children);

  static const String name = 'CourseRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const CourseScreen();
    },
  );
}

/// generated route for
/// [ExerciseScreen]
class ExerciseRoute extends PageRouteInfo<void> {
  const ExerciseRoute({List<PageRouteInfo>? children})
    : super(ExerciseRoute.name, initialChildren: children);

  static const String name = 'ExerciseRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ExerciseScreen();
    },
  );
}

/// generated route for
/// [HomePage]
class HomeRoute extends PageRouteInfo<void> {
  const HomeRoute({List<PageRouteInfo>? children})
    : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const HomePage();
    },
  );
}

/// generated route for
/// [LearnPage]
class LearnRoute extends PageRouteInfo<void> {
  const LearnRoute({List<PageRouteInfo>? children})
    : super(LearnRoute.name, initialChildren: children);

  static const String name = 'LearnRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const LearnPage();
    },
  );
}

/// generated route for
/// [MyBookScreen]
class MyBookRoute extends PageRouteInfo<void> {
  const MyBookRoute({List<PageRouteInfo>? children})
    : super(MyBookRoute.name, initialChildren: children);

  static const String name = 'MyBookRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MyBookScreen();
    },
  );
}

/// generated route for
/// [ProfilePage]
class ProfileRoute extends PageRouteInfo<void> {
  const ProfileRoute({List<PageRouteInfo>? children})
    : super(ProfileRoute.name, initialChildren: children);

  static const String name = 'ProfileRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ProfilePage();
    },
  );
}

/// generated route for
/// [Ranker_Screen]
class Ranker_Route extends PageRouteInfo<void> {
  const Ranker_Route({List<PageRouteInfo>? children})
    : super(Ranker_Route.name, initialChildren: children);

  static const String name = 'Ranker_Route';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const Ranker_Screen();
    },
  );
}

/// generated route for
/// [SavedWordsScreen]
class SavedWordsRoute extends PageRouteInfo<void> {
  const SavedWordsRoute({List<PageRouteInfo>? children})
    : super(SavedWordsRoute.name, initialChildren: children);

  static const String name = 'SavedWordsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SavedWordsScreen();
    },
  );
}

/// generated route for
/// [SettingsPage]
class SettingsRoute extends PageRouteInfo<void> {
  const SettingsRoute({List<PageRouteInfo>? children})
    : super(SettingsRoute.name, initialChildren: children);

  static const String name = 'SettingsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SettingsPage();
    },
  );
}

/// generated route for
/// [SignInScreen]
class SignInRoute extends PageRouteInfo<void> {
  const SignInRoute({List<PageRouteInfo>? children})
    : super(SignInRoute.name, initialChildren: children);

  static const String name = 'SignInRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SignInScreen();
    },
  );
}

/// generated route for
/// [SpeakingPracticeScreen]
class SpeakingPracticeRoute extends PageRouteInfo<void> {
  const SpeakingPracticeRoute({List<PageRouteInfo>? children})
    : super(SpeakingPracticeRoute.name, initialChildren: children);

  static const String name = 'SpeakingPracticeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const SpeakingPracticeScreen();
    },
  );
}

/// generated route for
/// [VocabularyDetailScreen]
class VocabularyDetailRoute extends PageRouteInfo<VocabularyDetailRouteArgs> {
  VocabularyDetailRoute({
    Key? key,
    required SavedWord word,
    List<PageRouteInfo>? children,
  }) : super(
         VocabularyDetailRoute.name,
         args: VocabularyDetailRouteArgs(key: key, word: word),
         initialChildren: children,
       );

  static const String name = 'VocabularyDetailRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<VocabularyDetailRouteArgs>();
      return VocabularyDetailScreen(key: args.key, word: args.word);
    },
  );
}

class VocabularyDetailRouteArgs {
  const VocabularyDetailRouteArgs({this.key, required this.word});

  final Key? key;

  final SavedWord word;

  @override
  String toString() {
    return 'VocabularyDetailRouteArgs{key: $key, word: $word}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! VocabularyDetailRouteArgs) return false;
    return key == other.key && word == other.word;
  }

  @override
  int get hashCode => key.hashCode ^ word.hashCode;
}

/// generated route for
/// [VocabularyScreen]
class VocabularyRoute extends PageRouteInfo<void> {
  const VocabularyRoute({List<PageRouteInfo>? children})
    : super(VocabularyRoute.name, initialChildren: children);

  static const String name = 'VocabularyRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const VocabularyScreen();
    },
  );
}
