import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'prv/conversation_provider.dart';
import '../ai_chat_screen/model/chat_message.dart';

@RoutePage()
class ConversationPracticeScreen extends StatelessWidget {
  final PracticeUser partner;

  const ConversationPracticeScreen({
    super.key,
    required this.partner,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ConversationProvider()..startConversation(partner),
      child: Scaffold(
        backgroundColor: Colors.grey[100],
        body: SafeArea(
          child: Consumer<ConversationProvider>(
            builder: (context, provider, child) {
              return Column(
                children: [
                  _buildHeader(context, provider),
                  _buildPartnerInfo(provider),
                  _buildConversationStarters(provider),
                  _buildCallControls(provider),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ConversationProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      color: Colors.white,
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              provider.endConversation();
              Navigator.of(context).pop();
            },
          ),
          const Spacer(),
          IconButton(
            icon: Icon(
              provider.isVolumeOn ? Icons.volume_up : Icons.volume_off,
              color: provider.isVolumeOn ? Colors.blue : Colors.grey,
            ),
            onPressed: provider.toggleVolume,
          ),
        ],
      ),
    );
  }

  Widget _buildPartnerInfo(ConversationProvider provider) {
    return Container(
      padding: const EdgeInsets.all(20),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage: NetworkImage(partner.avatar),
              ),
              const SizedBox(width: 8),
              CircleAvatar(
                radius: 20,
                backgroundImage: NetworkImage(
                  'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Sophia & ${partner.name}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            provider.formatCallDuration(),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationStarters(ConversationProvider provider) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Conversation Starters',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: provider.conversationStarters.length,
                itemBuilder: (context, index) {
                  final starter = provider.conversationStarters[index];
                  final isSelected =
                      provider.selectedConversationStarter == starter.id;

                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: Card(
                      elevation: isSelected ? 3 : 1,
                      color: isSelected ? Colors.blue[50] : Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(
                          color: isSelected ? Colors.blue : Colors.transparent,
                          width: 1,
                        ),
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        title: Text(
                          starter.question,
                          style: TextStyle(
                            fontSize: 14,
                            height: 1.4,
                            color:
                                isSelected ? Colors.blue[800] : Colors.black87,
                            fontWeight: isSelected
                                ? FontWeight.w500
                                : FontWeight.normal,
                          ),
                        ),
                        onTap: () {
                          provider.selectConversationStarter(starter.id);
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCallControls(ConversationProvider provider) {
    return Container(
      padding: const EdgeInsets.all(20),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildControlButton(
            icon: provider.isMuted ? Icons.mic_off : Icons.mic,
            label: 'Mute',
            isActive: provider.isMuted,
            onTap: provider.toggleMute,
          ),
          _buildControlButton(
            icon: provider.isVolumeOn ? Icons.volume_up : Icons.volume_off,
            label: 'Volume',
            isActive: !provider.isVolumeOn,
            onTap: provider.toggleVolume,
          ),
          _buildControlButton(
            icon: Icons.call_end,
            label: 'End Call',
            isActive: false,
            backgroundColor: Colors.red,
            onTap: () {
              provider.endConversation();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required bool isActive,
    Color? backgroundColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: backgroundColor ??
              (isActive ? Colors.grey[300] : Colors.grey[100]),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: backgroundColor != null ? Colors.white : Colors.black87,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: backgroundColor != null ? Colors.white : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
