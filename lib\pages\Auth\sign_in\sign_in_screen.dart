import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import '../../../core/app_export.dart';
import '../../../routes/app_router.dart';
import 'prv/auth_provider.dart';
import 'widgets/custom_text_field.dart';
import 'widgets/auth_button.dart';

@RoutePage()
class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.router.pop(),
        ),
        title: const Text(
          'Sign in',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),
                  _buildLogo(),
                  const SizedBox(height: 40),
                  _buildSignInForm(authProvider),
                  const SizedBox(height: 20),
                  _buildForgotPassword(),
                  const SizedBox(height: 30),
                  _buildSocialButtons(authProvider),
                  const SizedBox(height: 30),
                  _buildRegisterLink(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.blue[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.chat_bubble_outline,
            color: Colors.blue,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        const Text(
          'Speak up',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildSignInForm(AuthProvider authProvider) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          CustomTextField(
            controller: _emailController,
            hintText: 'Email',
            prefixIcon: Icons.email_outlined,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter your email';
              }
              if (!value!.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _passwordController,
            hintText: 'Password',
            prefixIcon: Icons.lock_outline,
            isPassword: true,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter your password';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),
          if (authProvider.error != null)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      authProvider.error!,
                      style: TextStyle(color: Colors.red[600], fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: authProvider.isLoading
                  ? null
                  : () => _handleSignIn(authProvider),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: authProvider.isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Login',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForgotPassword() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'Forgot your password? ',
          style: TextStyle(color: Colors.grey),
        ),
        GestureDetector(
          onTap: () {
            // TODO: Navigate to forgot password page
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                  content: Text('Forgot password feature coming soon!')),
            );
          },
          child: const Text(
            'Reset your password',
            style: TextStyle(
              color: Colors.blue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialButtons(AuthProvider authProvider) {
    return Column(
      children: [
        AuthButton(
          text: 'Sign up with Apple',
          icon: Icons.apple,
          backgroundColor: Colors.grey[100]!,
          textColor: Colors.black,
          onPressed: authProvider.isLoading
              ? null
              : () => _handleAppleSignIn(authProvider),
        ),
        const SizedBox(height: 12),
        AuthButton(
          text: 'Sign in with Google',
          icon: Icons.g_mobiledata,
          backgroundColor: Colors.grey[100]!,
          textColor: Colors.black,
          onPressed: authProvider.isLoading
              ? null
              : () => _handleGoogleSignIn(authProvider),
        ),
        const SizedBox(height: 12),
        AuthButton(
          text: 'Sign in with SMS',
          icon: Icons.sms,
          backgroundColor: Colors.grey[100]!,
          textColor: Colors.black,
          onPressed: authProvider.isLoading
              ? null
              : () {
                  // TODO: Navigate to phone auth page
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Phone auth feature coming soon!')),
                  );
                },
        ),
      ],
    );
  }

  Widget _buildRegisterLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'Are you not registered? ',
          style: TextStyle(color: Colors.grey),
        ),
        GestureDetector(
          onTap: () {
            // TODO: Navigate to register page
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Register feature coming soon!')),
            );
          },
          child: const Text(
            'Register',
            style: TextStyle(
              color: Colors.blue,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleSignIn(AuthProvider authProvider) async {
    if (_formKey.currentState?.validate() ?? false) {
      authProvider.clearError();
      final success = await authProvider.signInWithEmail(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      if (success && mounted) {
        context.router.pushAndPopUntil(
          const ApplicationRoute(),
          predicate: (route) => false,
        );
      }
    }
  }

  Future<void> _handleGoogleSignIn(AuthProvider authProvider) async {
    authProvider.clearError();
    final success = await authProvider.signInWithGoogle();

    if (success && mounted) {
      context.router.pushAndPopUntil(
        const ApplicationRoute(),
        predicate: (route) => false,
      );
    }
  }

  Future<void> _handleAppleSignIn(AuthProvider authProvider) async {
    authProvider.clearError();
    final success = await authProvider.signInWithApple();

    if (success && mounted) {
      context.router.pushAndPopUntil(
        const ApplicationRoute(),
        predicate: (route) => false,
      );
    }
  }
}
