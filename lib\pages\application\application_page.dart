import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import '../../core/app_export.dart';
import 'provider/application_provider.dart';
import 'widgets/application_widgets.dart';

@RoutePage()
class ApplicationPage extends StatefulWidget {
  const ApplicationPage({Key? key}) : super(key: key);

  @override
  State<ApplicationPage> createState() => _ApplicationPageState();
}

class _ApplicationPageState extends State<ApplicationPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ApplicationProvider>(
      builder: (context, provider, child) {
        return Container(
          color: Colors.white,
          child: SafeArea(
            child: Scaffold(
              body: buildPage(provider.currentIndex),
              bottomNavigationBar: Container(
                width: 375.h,
                height: 58.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.h),
                    topRight: Radius.circular(20.h),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 1,
                    )
                  ],
                ),
                child: BottomNavigationBar(
                  currentIndex: provider.currentIndex,
                  onTap: (value) {
                    provider.changeIndex(value);
                  },
                  elevation: 0,
                  type: BottomNavigationBarType.fixed,
                  selectedItemColor: Theme.of(context).primaryColor,
                  unselectedItemColor: Colors.grey[600],
                  items: bottomTabs,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
