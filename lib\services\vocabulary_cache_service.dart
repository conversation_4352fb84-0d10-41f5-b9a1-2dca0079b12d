import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../screens/vocabulary_screen/model/vocabulary_item.dart';
import 'vocabulary_service.dart';

class VocabularyCacheService {
  static final VocabularyCacheService _instance = VocabularyCacheService._internal();
  factory VocabularyCacheService() => _instance;
  VocabularyCacheService._internal();

  static const String _cacheKey = 'vocabulary_cache';
  static const String _cacheTimestampKey = 'vocabulary_cache_timestamp';
  static const int _cacheHours = 24; // Cache 24 giờ

  List<VocabularyWord>? _cachedVocabularies;
  bool _isLoading = false;
  bool _isInitialized = false;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  bool get hasCachedData => _cachedVocabularies != null && _cachedVocabularies!.isNotEmpty;
  List<VocabularyWord> get cachedVocabularies => _cachedVocabularies ?? [];

  /// Initialize cache service - g<PERSON><PERSON> khi app khởi động
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    print('🔄 Initializing vocabulary cache...');
    
    try {
      // Load từ cache trước
      await _loadFromCache();
      
      // Nếu cache hết hạn hoặc không có, load từ Firebase
      final isValid = await _isCacheValid();
      if (!isValid || !hasCachedData) {
        print('📡 Cache expired or empty, loading from Firebase...');
        await _loadFromFirebaseAndCache();
      } else {
        print('✅ Using cached vocabulary data');
      }
      
      _isInitialized = true;
      print('✅ Vocabulary cache initialized with ${_cachedVocabularies?.length ?? 0} words');
    } catch (e) {
      print('❌ Error initializing vocabulary cache: $e');
      _isInitialized = true; // Vẫn đánh dấu initialized để không retry liên tục
    }
  }

  /// Load vocabularies từ cache
  Future<void> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_cacheKey);
      
      if (cachedData != null) {
        final List<dynamic> jsonList = json.decode(cachedData);
        _cachedVocabularies = jsonList
            .map((json) => VocabularyWord.fromJson(json as Map<String, dynamic>))
            .toList();
        print('📱 Loaded ${_cachedVocabularies!.length} vocabularies from cache');
      }
    } catch (e) {
      print('❌ Error loading from cache: $e');
      _cachedVocabularies = null;
    }
  }

  /// Kiểm tra cache có hợp lệ không
  Future<bool> _isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_cacheTimestampKey);
      
      if (timestamp == null) {
        return false;
      }
      
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final hoursDiff = DateTime.now().difference(cacheTime).inHours;
      
      return hoursDiff < _cacheHours;
    } catch (e) {
      return false;
    }
  }

  /// Load từ Firebase và cache lại
  Future<void> _loadFromFirebaseAndCache() async {
    if (_isLoading) return;
    
    _isLoading = true;
    
    try {
      final vocabularyService = VocabularyService();
      await vocabularyService.initializeFirebase();
      await vocabularyService.loadVocabulariesFromFirebase();
      
      _cachedVocabularies = vocabularyService.allWords;
      
      // Cache vào SharedPreferences
      await _saveToCache();
      
      print('✅ Loaded and cached ${_cachedVocabularies!.length} vocabularies from Firebase');
    } catch (e) {
      print('❌ Error loading from Firebase: $e');
      // Nếu lỗi, vẫn giữ cache cũ nếu có
    } finally {
      _isLoading = false;
    }
  }

  /// Save vào cache
  Future<void> _saveToCache() async {
    if (_cachedVocabularies == null) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _cachedVocabularies!.map((word) => word.toJson()).toList();
      final jsonString = json.encode(jsonList);
      
      await prefs.setString(_cacheKey, jsonString);
      await prefs.setInt(_cacheTimestampKey, DateTime.now().millisecondsSinceEpoch);
      
      print('💾 Cached ${_cachedVocabularies!.length} vocabularies');
    } catch (e) {
      print('❌ Error saving to cache: $e');
    }
  }

  /// Get vocabularies by level
  List<VocabularyWord> getVocabulariesByLevel(String level) {
    if (!hasCachedData) return [];
    
    return _cachedVocabularies!
        .where((word) => word.level == level)
        .toList();
  }

  /// Get all vocabularies
  List<VocabularyWord> getAllVocabularies() {
    return _cachedVocabularies ?? [];
  }

  /// Search vocabularies
  List<VocabularyWord> searchVocabularies(String query) {
    if (!hasCachedData || query.isEmpty) return [];
    
    final lowerQuery = query.toLowerCase();
    return _cachedVocabularies!.where((word) {
      return word.word.toLowerCase().contains(lowerQuery) ||
          word.vietnameseMeaning.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// Get statistics
  Map<String, int> getStatistics() {
    if (!hasCachedData) return {};
    
    final stats = <String, int>{};
    
    for (final word in _cachedVocabularies!) {
      stats[word.level] = (stats[word.level] ?? 0) + 1;
    }
    
    stats['total'] = _cachedVocabularies!.length;
    return stats;
  }

  /// Force refresh từ Firebase
  Future<void> forceRefresh() async {
    print('🔄 Force refreshing vocabulary cache...');
    await _loadFromFirebaseAndCache();
  }

  /// Clear cache
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimestampKey);
      
      _cachedVocabularies = null;
      print('🗑️ Vocabulary cache cleared');
    } catch (e) {
      print('❌ Error clearing cache: $e');
    }
  }
}
