import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';

class AuthProvider with ChangeNotifier {
  // Direct properties instead of state object
  bool _isLoading = false;
  bool _isAuthenticated = false;
  UserModel? _user;
  String? _error;
  AuthProviderType? _lastUsedProvider;

  // Getters
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  UserModel? get user => _user;
  String? get error => _error;
  AuthProviderType? get lastUsedProvider => _lastUsedProvider;

  AuthProvider() {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token != null) {
        // In a real app, you would validate the token with your backend
        // For demo: Enable auto-login if you want, disable if you want to test login flow

        // Option 1: Auto-login (uncomment these lines)
        // _isAuthenticated = true;
        // _user = UserModel(
        //   id: '1',
        //   email: '<EMAIL>',
        //   fullName: 'Demo User',
        //   createdAt: DateTime.now(),
        // );
        // notifyListeners();

        // Option 2: Force login screen (current - for testing)
        await prefs.remove('auth_token');
      }
    } catch (e) {
      debugPrint('Error checking auth status: $e');
    }
  }

  Future<bool> signInWithEmail({
    required String email,
    required String password,
  }) async {
    _setLoading(true);

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // Mock authentication - accept any email/password for demo
      if (email.isNotEmpty && password.isNotEmpty) {
        final user = UserModel(
          id: '1',
          email: email,
          fullName: 'Demo User',
          createdAt: DateTime.now(),
          isEmailVerified: true,
        );

        await _saveAuthData(user, 'demo_token_123');
        _isAuthenticated = true;
        _user = user;
        _error = null;
        _lastUsedProvider = AuthProviderType.email;
        _setLoading(false);
        return true;
      } else {
        _error = 'Email và mật khẩu không được để trống';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = e.toString();
      _setLoading(false);
      return false;
    }
  }

  Future<bool> signInWithGoogle() async {
    _setLoading(true);

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // Mock Google sign in
      final user = UserModel(
        id: '2',
        email: '<EMAIL>',
        fullName: 'Google User',
        createdAt: DateTime.now(),
        isEmailVerified: true,
      );

      await _saveAuthData(user, 'google_token_123');
      _isAuthenticated = true;
      _user = user;
      _error = null;
      _lastUsedProvider = AuthProviderType.google;
      _setLoading(false);
      return true;
    } catch (e) {
      _error = e.toString();
      _setLoading(false);
      return false;
    }
  }

  Future<bool> signInWithApple() async {
    _setLoading(true);

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // Mock Apple sign in
      final user = UserModel(
        id: '3',
        email: '<EMAIL>',
        fullName: 'Apple User',
        createdAt: DateTime.now(),
        isEmailVerified: true,
      );

      await _saveAuthData(user, 'apple_token_123');
      _isAuthenticated = true;
      _user = user;
      _error = null;
      _lastUsedProvider = AuthProviderType.apple;
      _setLoading(false);
      return true;
    } catch (e) {
      _error = e.toString();
      _setLoading(false);
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _clearAuthData();
      _isLoading = false;
      _isAuthenticated = false;
      _user = null;
      _error = null;
      _lastUsedProvider = null;
      notifyListeners();
    } catch (e) {
      debugPrint('Error signing out: $e');
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  Future<void> _saveAuthData(UserModel user, String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
    await prefs.setString('user_email', user.email);
    await prefs.setString('user_name', user.fullName);
  }

  Future<void> _clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    await prefs.remove('user_email');
    await prefs.remove('user_name');
  }
}
