import 'package:flutter/material.dart';
import '../../../screens/ai_chat_screen/model/chat_message.dart';

class BanBeProvider with ChangeNotifier {
  List<PracticeUser> _availableUsers = [];
  List<PracticeUser> _filteredUsers = [];
  String _searchQuery = '';
  bool _isLoading = false;

  // Getters
  List<PracticeUser> get availableUsers => _filteredUsers;
  String get searchQuery => _searchQuery;
  bool get isLoading => _isLoading;

  BanBeProvider() {
    _initializeUsers();
  }

  void _initializeUsers() {
    _availableUsers = [
      PracticeUser(
        id: '1',
        name: '<PERSON>',
        level: 'Advanced',
        avatar:
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        isOnline: true,
      ),
      PracticeUser(
        id: '2',
        name: '<PERSON>',
        level: 'Intermediate',
        avatar:
            'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        isOnline: true,
      ),
      PracticeUser(
        id: '3',
        name: '<PERSON>',
        level: 'Begin<PERSON>',
        avatar:
            'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        isOnline: false,
      ),
      PracticeUser(
        id: '4',
        name: 'Olivia',
        level: 'Advanced',
        avatar:
            'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
        isOnline: true,
      ),
      PracticeUser(
        id: '5',
        name: 'Noah',
        level: 'Intermediate',
        avatar:
            'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face',
        isOnline: false,
      ),
    ];
    _filteredUsers = List.from(_availableUsers);
  }

  void searchUsers(String query) {
    _searchQuery = query;

    if (query.isEmpty) {
      _filteredUsers = List.from(_availableUsers);
    } else {
      _filteredUsers = _availableUsers
          .where((user) =>
              user.name.toLowerCase().contains(query.toLowerCase()) ||
              user.level.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }

    notifyListeners();
  }

  void refreshUsers() {
    _isLoading = true;
    notifyListeners();

    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      // Randomly update online status
      for (var user in _availableUsers) {
        user.isOnline = DateTime.now().millisecond % 2 == 0;
      }

      _isLoading = false;
      searchUsers(_searchQuery); // Reapply search filter
    });
  }

  List<PracticeUser> getOnlineUsers() {
    return _availableUsers.where((user) => user.isOnline).toList();
  }

  PracticeUser? getUserById(String id) {
    try {
      return _availableUsers.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  void inviteFriend(String email) {
    // Simulate invite functionality
    // In real app, this would call an API
  }
}
