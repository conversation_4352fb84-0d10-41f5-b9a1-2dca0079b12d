import 'package:flutter/material.dart';
import '../../saved_words_screen/model/saved_word.dart';

class MyBookProvider with ChangeNotifier {
  List<SavedWord> _savedWords = [];
  List<BookSection> _bookSections = [];
  String _selectedSection = 'vocabulary';
  int _dailyStreak = 7;
  int _newWordsCount = 25;
  int _totalPoints = 1250;

  // Getters
  List<SavedWord> get savedWords => _savedWords;
  List<BookSection> get bookSections => _bookSections;
  String get selectedSection => _selectedSection;
  int get dailyStreak => _dailyStreak;
  int get newWordsCount => _newWordsCount;
  int get totalPoints => _totalPoints;

  MyBookProvider() {
    _initializeData();
  }

  void _initializeData() {
    _initializeSavedWords();
    _initializeBookSections();
  }

  void _initializeSavedWords() {
    _savedWords = [
      SavedWord(
        word: 'as many as',
        pronunciation: '/æz ˈmeni æz/',
        meaning: 'nhiều như',
        example: 'You can take as many as you want.',
        savedDate: DateTime.now().subtract(const Duration(days: 1)),
        reviewCount: 3,
      ),
      SavedWord(
        word: 'City break',
        pronunciation: '/ˈsɪti breɪk/',
        meaning: 'kỳ nghỉ ngắn ở thành phố',
        example: 'We went on a city break to Paris.',
        savedDate: DateTime.now().subtract(const Duration(days: 2)),
        reviewCount: 1,
      ),
      SavedWord(
        word: 'Cosmopolitan',
        pronunciation: '/ˌkɒzməˈpɒlɪtən/',
        meaning: 'thuộc về thế giới',
        example: 'New York is a cosmopolitan city.',
        savedDate: DateTime.now().subtract(const Duration(days: 3)),
        reviewCount: 2,
      ),
      SavedWord(
        word: 'Crowded',
        pronunciation: '/ˈkraʊdɪd/',
        meaning: 'đông đúc',
        example: 'The street was very crowded.',
        savedDate: DateTime.now().subtract(const Duration(days: 4)),
        reviewCount: 5,
      ),
      SavedWord(
        word: 'Embassy',
        pronunciation: '/ˈembəsi/',
        meaning: 'đại sứ quán',
        example: 'I need to visit the embassy.',
        savedDate: DateTime.now().subtract(const Duration(days: 5)),
        reviewCount: 1,
      ),
    ];
  }

  void _initializeBookSections() {
    _bookSections = [
      BookSection(
        title: 'Từ vựng',
        subtitle: '120 từ',
        color: Colors.red,
        icon: Icons.book,
        itemCount: 120,
        progress: 0.75,
      ),
      BookSection(
        title: 'Ngữ pháp',
        subtitle: '45 bài',
        color: Colors.yellow[700]!,
        icon: Icons.school,
        itemCount: 45,
        progress: 0.60,
      ),
      BookSection(
        title: 'Bài tập',
        subtitle: '80 bài',
        color: Colors.green,
        icon: Icons.assignment,
        itemCount: 80,
        progress: 0.85,
      ),
      BookSection(
        title: 'Lộ trình',
        subtitle: '12 cấp độ',
        color: Colors.blue,
        icon: Icons.route,
        itemCount: 12,
        progress: 0.40,
      ),
    ];
  }

  void setSelectedSection(String section) {
    _selectedSection = section;
    notifyListeners();
  }

  void addSavedWord(SavedWord word) {
    _savedWords.add(word);
    _newWordsCount++;
    notifyListeners();
  }

  void removeSavedWord(SavedWord word) {
    _savedWords.remove(word);
    notifyListeners();
  }

  void updateWordReviewCount(String wordId) {
    final wordIndex = _savedWords.indexWhere((word) => word.word == wordId);
    if (wordIndex != -1) {
      final word = _savedWords[wordIndex];
      _savedWords[wordIndex] = SavedWord(
        word: word.word,
        pronunciation: word.pronunciation,
        meaning: word.meaning,
        example: word.example,
        savedDate: word.savedDate,
        reviewCount: word.reviewCount + 1,
        isFavorite: word.isFavorite,
      );
      notifyListeners();
    }
  }

  void toggleWordFavorite(String wordId) {
    final wordIndex = _savedWords.indexWhere((word) => word.word == wordId);
    if (wordIndex != -1) {
      final word = _savedWords[wordIndex];
      _savedWords[wordIndex] = SavedWord(
        word: word.word,
        pronunciation: word.pronunciation,
        meaning: word.meaning,
        example: word.example,
        savedDate: word.savedDate,
        reviewCount: word.reviewCount,
        isFavorite: !word.isFavorite,
      );
      notifyListeners();
    }
  }

  List<SavedWord> getFavoriteWords() {
    return _savedWords.where((word) => word.isFavorite).toList();
  }

  List<SavedWord> getRecentWords() {
    final sortedWords = List<SavedWord>.from(_savedWords);
    sortedWords.sort((a, b) => b.savedDate.compareTo(a.savedDate));
    return sortedWords.take(10).toList();
  }

  void updateStreak() {
    _dailyStreak++;
    _totalPoints += 10;
    notifyListeners();
  }

  BookSection? getSectionByTitle(String title) {
    try {
      return _bookSections.firstWhere((section) => section.title == title);
    } catch (e) {
      return null;
    }
  }
}
