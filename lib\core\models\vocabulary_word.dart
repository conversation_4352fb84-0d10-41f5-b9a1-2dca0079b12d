class VocabularyWord {
  final int? id;
  final String word;
  final String? pronunciation;
  final String? meaning;
  final String? definition;
  final String? example;
  final String? audioUrl;
  final String? imageUrl;
  final int difficultyLevel;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  VocabularyWord({
    this.id,
    required this.word,
    this.pronunciation,
    this.meaning,
    this.definition,
    this.example,
    this.audioUrl,
    this.imageUrl,
    this.difficultyLevel = 1,
    this.createdAt,
    this.updatedAt,
  });

  factory VocabularyWord.fromMap(Map<String, dynamic> map) {
    return VocabularyWord(
      id: map['id'],
      word: map['word'],
      pronunciation: map['pronunciation'],
      meaning: map['meaning'],
      definition: map['definition'],
      example: map['example'],
      audioUrl: map['audio_url'],
      imageUrl: map['image_url'],
      difficultyLevel: map['difficulty_level'] ?? 1,
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  factory VocabularyWord.fromApiResponse(Map<String, dynamic> apiData) {
    // Parse data from dictionary API response
    final meanings = apiData['meanings'] as List?;
    final phonetics = apiData['phonetics'] as List?;
    
    String? pronunciation;
    String? audioUrl;
    
    if (phonetics != null && phonetics.isNotEmpty) {
      for (var phonetic in phonetics) {
        if (phonetic['text'] != null) {
          pronunciation = phonetic['text'];
        }
        if (phonetic['audio'] != null && phonetic['audio'].toString().isNotEmpty) {
          audioUrl = phonetic['audio'];
        }
      }
    }

    String? definition;
    String? example;
    
    if (meanings != null && meanings.isNotEmpty) {
      final firstMeaning = meanings.first;
      final definitions = firstMeaning['definitions'] as List?;
      
      if (definitions != null && definitions.isNotEmpty) {
        final firstDef = definitions.first;
        definition = firstDef['definition'];
        example = firstDef['example'];
      }
    }

    return VocabularyWord(
      word: apiData['word'],
      pronunciation: pronunciation,
      definition: definition,
      example: example,
      audioUrl: audioUrl,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'word': word,
      'pronunciation': pronunciation,
      'meaning': meaning,
      'definition': definition,
      'example': example,
      'audio_url': audioUrl,
      'image_url': imageUrl,
      'difficulty_level': difficultyLevel,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  VocabularyWord copyWith({
    int? id,
    String? word,
    String? pronunciation,
    String? meaning,
    String? definition,
    String? example,
    String? audioUrl,
    String? imageUrl,
    int? difficultyLevel,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VocabularyWord(
      id: id ?? this.id,
      word: word ?? this.word,
      pronunciation: pronunciation ?? this.pronunciation,
      meaning: meaning ?? this.meaning,
      definition: definition ?? this.definition,
      example: example ?? this.example,
      audioUrl: audioUrl ?? this.audioUrl,
      imageUrl: imageUrl ?? this.imageUrl,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
