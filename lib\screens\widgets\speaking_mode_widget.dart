import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../vocabulary_screen/prv/vocabulary_provider.dart';

class SpeakingModeWidget extends StatefulWidget {
  const SpeakingModeWidget({super.key});

  @override
  State<SpeakingModeWidget> createState() => _SpeakingModeWidgetState();
}

class _SpeakingModeWidgetState extends State<SpeakingModeWidget> {
  bool _isRevealed = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<VocabularyProvider>(
      builder: (context, provider, child) {
        final item = provider.currentItem;

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const SizedBox(height: 40),
              // Hiển thị từ tiếng Anh
              Text(
                item.word,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Kéo xuống để xem đáp án',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
              const SizedBox(height: 40),
              Expanded(
                child: GestureDetector(
                  onPanUpdate: (details) {
                    // Kéo xuống để hiện đáp án
                    if (details.delta.dy > 5) {
                      setState(() {
                        _isRevealed = true;
                      });
                    }
                    // Kéo lên để che đáp án
                    else if (details.delta.dy < -5) {
                      setState(() {
                        _isRevealed = false;
                      });
                    }
                  },
                  child: Container(
                    width: double.infinity,
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: BoxDecoration(
                      color: _isRevealed ? Colors.blue[50] : Colors.grey[400],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            _isRevealed ? Colors.blue[200]! : Colors.grey[400]!,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (!_isRevealed) ...[
                          const Icon(
                            Icons.keyboard_arrow_down,
                            size: 40,
                            color: Colors.white,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Kéo để xem đáp án',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ] else ...[
                          // Hiển thị phiên âm
                          Text(
                            item.pronunciation,
                            style: TextStyle(
                              fontSize: 20,
                              color: Colors.blue[800],
                              fontStyle: FontStyle.italic,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Hiển thị nghĩa
                          Text(
                            item.meaning,
                            style: TextStyle(
                              fontSize: 24,
                              color: Colors.blue[900],
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          // Hiển thị ví dụ
                          if (item.example.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.all(12),
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              decoration: BoxDecoration(
                                color: Colors.blue[100],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                item.example,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.blue[800],
                                  fontStyle: FontStyle.italic,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          const SizedBox(height: 16),
                          const Icon(
                            Icons.keyboard_arrow_up,
                            size: 40,
                            color: Colors.blue,
                          ),
                          const Text(
                            'Kéo lên để che',
                            style: TextStyle(
                              color: Colors.blue,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Navigation buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: provider.currentIndex > 0
                          ? () {
                              provider.previousItem();
                              setState(() {
                                _isRevealed = false;
                              });
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[600],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Trước'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: provider.currentIndex <
                              provider.vocabularyItems.length - 1
                          ? () {
                              provider.nextItem();
                              setState(() {
                                _isRevealed = false;
                              });
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Tiếp'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
