import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:learning_english_smart/pages/Learn_Page/provider/learn_provider.dart';
import 'package:learning_english_smart/pages/Learn_Page/widgets/learn_item_widget.dart';
import 'package:learning_english_smart/widgets/custom_app_bar.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_bottom_bar.dart';

@RoutePage()
class LearnPage extends StatefulWidget {
  const LearnPage({Key? key}) : super(key: key);

  @override
  LearnPageState createState() => LearnPageState();
}

class LearnPageState extends State<LearnPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
          title: 'Học tập',
          // showBackButton: true,
          // onBackPressed: () => context.router.pop(),
        ),
        body: Consumer<LearnPrv>(// Modified: Fixed type argument
            builder: (context, provider, child) {
          return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                    padding: EdgeInsets.fromLTRB(16.h, 20.h, 16.h, 0.h),
                    child: Text('Lộ trình học tập',
                        style: TextStyleHelper.instance.title22Bold
                            .copyWith(color: appTheme.colorFF1114))),
                Expanded(
                    child: ListView.builder(
                        padding: EdgeInsets.only(top: 16.h),
                        itemCount: provider.learnItemList
                            .length, // Modified: Removed null assertion
                        itemBuilder: (context, index) {
                          return LearnItemWidget(
                              lessonItem: provider.learnItemList[
                                  index], // Modified: Removed null assertion
                              onTap: () => provider.openLearn(
                                  context,
                                  provider.learnItemList[
                                      index])); // Modified: Removed null assertion
                        })),
              ]);
        }));
  }
}
