import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../vocabulary_screen/prv/vocabulary_provider.dart';
import '../vocabulary_detail_screen/vocabulary_detail_screen.dart';
import '../saved_words_screen/model/saved_word.dart';
import 'dart:math';

class MultipleChoiceWidget extends StatefulWidget {
  const MultipleChoiceWidget({super.key});

  @override
  State<MultipleChoiceWidget> createState() => _MultipleChoiceWidgetState();
}

class _MultipleChoiceWidgetState extends State<MultipleChoiceWidget> {
  List<String> _choices = [];
  String? _selectedAnswer;
  bool _showResult = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _generateChoices();
    });
  }

  void _generateChoices() {
    final provider = Provider.of<VocabularyProvider>(context, listen: false);
    final currentItem = provider.currentItem;
    final allItems = provider.vocabularyItems;

    // Tạo 4 đáp án: 1 đúng + 3 sai
    final wrongAnswers = allItems
        .where((item) => item.meaning != currentItem.meaning)
        .map((item) => item.meaning)
        .toList();

    wrongAnswers.shuffle(Random());

    _choices = [
      currentItem.meaning, // Đáp án đúng
      ...wrongAnswers.take(3), // 3 đáp án sai
    ];

    _choices.shuffle(Random()); // Trộn vị trí

    setState(() {
      _selectedAnswer = null;
      _showResult = false;
    });
  }

  void _selectAnswer(String answer) {
    if (_showResult) return;

    setState(() {
      _selectedAnswer = answer;
      _showResult = true;
    });

    final provider = Provider.of<VocabularyProvider>(context, listen: false);
    final isCorrect = answer == provider.currentItem.meaning;

    if (isCorrect) {
      // Đáp án đúng - cộng combo
      provider.incrementCombo();

      // Hiển thị thông báo với combo
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('🎉 Chính xác! Combo: ${provider.currentCombo}'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 1),
        ),
      );

      // Hiển thị VocabularyDetailScreen
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          final currentItem = provider.currentItem;
          final savedWord = SavedWord(
            word: currentItem.word,
            meaning: currentItem.meaning,
            pronunciation: currentItem.pronunciation,
            example: currentItem.example,
            savedDate: DateTime.now(),
          );

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VocabularyDetailScreen(
                word: savedWord,
              ),
            ),
          );
        }
      });
    } else {
      // Đáp án sai - hiển thị dialog
      _showWrongAnswerDialog(provider);
    }
  }

  void _showWrongAnswerDialog(VocabularyProvider provider) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            '❌ Đáp án sai!',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            'Bạn có muốn thử lại hay xem đáp án đúng?',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Reset để thử lại
                _generateChoices();
              },
              child: const Text(
                'Thử lại',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Reset combo khi xem đáp án
                provider.resetCombo();

                // Hiển thị đáp án đúng
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Đáp án đúng là: ${provider.currentItem.meaning}\nCombo đã được reset!',
                    ),
                    backgroundColor: Colors.orange,
                    duration: const Duration(seconds: 3),
                  ),
                );

                // Reset sau 3 giây
                Future.delayed(const Duration(seconds: 3), () {
                  if (mounted) {
                    _generateChoices();
                  }
                });
              },
              child: const Text(
                'Hiển thị đáp án',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VocabularyProvider>(
      builder: (context, provider, child) {
        final item = provider.currentItem;

        // Regenerate choices when item changes
        if (_choices.isEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _generateChoices();
          });
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Combo display
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: provider.currentCombo > 0
                      ? Colors.orange[100]
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color:
                        provider.currentCombo > 0 ? Colors.orange : Colors.grey,
                    width: 2,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.local_fire_department,
                      color: provider.currentCombo > 0
                          ? Colors.orange
                          : Colors.grey,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Combo: ${provider.currentCombo}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: provider.currentCombo > 0
                            ? Colors.orange[800]
                            : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Text(
                item.word,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                item.pronunciation,
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
              const SizedBox(height: 40),
              Expanded(
                child: ListView.builder(
                  itemCount: _choices.length,
                  itemBuilder: (context, index) {
                    final choice = _choices[index];
                    final isSelected = _selectedAnswer == choice;
                    final isCorrect = choice == item.meaning;

                    Color backgroundColor = Colors.grey[200]!;
                    if (_showResult && isSelected) {
                      backgroundColor =
                          isCorrect ? Colors.green[100]! : Colors.red[100]!;
                    } else if (isSelected) {
                      backgroundColor = Colors.blue[100]!;
                    }

                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: ElevatedButton(
                        onPressed: () => _selectAnswer(choice),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: backgroundColor,
                          foregroundColor: Colors.black87,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          choice,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              // Row(
              //   children: [
              //     Expanded(
              //       child: ElevatedButton(
              //         onPressed: () {},
              //         style: ElevatedButton.styleFrom(
              //           backgroundColor: Colors.red,
              //           foregroundColor: Colors.white,
              //           shape: RoundedRectangleBorder(
              //             borderRadius: BorderRadius.circular(8),
              //           ),
              //         ),
              //         child: const Text('Đúng'),
              //       ),
              //     ),
              //     const SizedBox(width: 12),
              //     Expanded(
              //       child: ElevatedButton(
              //         onPressed: () {},
              //         style: ElevatedButton.styleFrom(
              //           backgroundColor: Colors.cyan,
              //           foregroundColor: Colors.white,
              //           shape: RoundedRectangleBorder(
              //             borderRadius: BorderRadius.circular(8),
              //           ),
              //         ),
              //         child: const Text('Kiểm tra'),
              //       ),
              //     ),
              //   ],
              // ),
            ],
          ),
        );
      },
    );
  }
}
