import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../vocabulary_screen/prv/vocabulary_provider.dart';
import 'study_mode_widget.dart';
import 'dart:math';

class MultipleChoiceWidget extends StatefulWidget {
  const MultipleChoiceWidget({super.key});

  @override
  State<MultipleChoiceWidget> createState() => _MultipleChoiceWidgetState();
}

class _MultipleChoiceWidgetState extends State<MultipleChoiceWidget> {
  List<String> _choices = [];
  String? _selectedAnswer;
  bool _showResult = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _generateChoices();
    });
  }

  void _generateChoices() {
    final provider = Provider.of<VocabularyProvider>(context, listen: false);
    final currentItem = provider.currentItem;
    final allItems = provider.vocabularyItems;

    // Tạo 4 đáp án: 1 đúng + 3 sai
    final wrongAnswers = allItems
        .where((item) => item.meaning != currentItem.meaning)
        .map((item) => item.meaning)
        .toList();

    wrongAnswers.shuffle(Random());

    _choices = [
      currentItem.meaning, // Đáp án đúng
      ...wrongAnswers.take(3), // 3 đáp án sai
    ];

    _choices.shuffle(Random()); // Trộn vị trí

    setState(() {
      _selectedAnswer = null;
      _showResult = false;
    });
  }

  void _selectAnswer(String answer) {
    if (_showResult) return; // Đã check kết quả rồi thì không cho chọn nữa

    setState(() {
      _selectedAnswer = answer;
    });
  }

  void _checkAnswer() {
    if (_selectedAnswer == null) return;

    final provider = Provider.of<VocabularyProvider>(context, listen: false);
    final isCorrect = _selectedAnswer == provider.currentItem.meaning;

    setState(() {
      _showResult = true;
    });

    if (isCorrect) {
      // Đáp án đúng - cộng combo
      provider.incrementCombo();

      // Hiển thị thông báo với combo
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('🎉 Chính xác! Combo: ${provider.currentCombo}'),
          backgroundColor: Colors.green,
          duration: const Duration(milliseconds: 800),
        ),
      );

      // Chuyển sang StudyModeWidget ngay lập tức
      _showStudyModeWidget();
    } else {
      // Đáp án sai - hiển thị bottom sheet
      _showWrongAnswerBottomSheet(provider);
    }
  }

  void _showStudyModeWidget() {
    // Lấy từ hiện tại để fix
    final provider = Provider.of<VocabularyProvider>(context, listen: false);
    final currentItem = provider.currentItem;

    // Chuyển sang StudyModeWidget với smooth animation
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => Scaffold(
          appBar: AppBar(
            title: const Text('Học từ vựng'),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
          body: StudyModeWidget(
            showNextButton: true,
            fixedItem: currentItem, // Truyền từ hiện tại để fix
            onNext: () {
              // Smooth animation khi quay lại
              Navigator.of(context).pop();

              // Chờ animation hoàn thành rồi mới chuyển từ và generate choices
              Future.delayed(const Duration(milliseconds: 300), () {
                if (mounted) {
                  provider.nextItem();
                  _generateChoices(); // Generate choices cho từ tiếp theo
                }
              });
            },
          ),
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // Slide transition từ phải sang trái
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
        reverseTransitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  void _showWrongAnswerBottomSheet(VocabularyProvider provider) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      backgroundColor: Colors.red[50],
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            border: Border.all(color: Colors.red, width: 2),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Error icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(height: 16),

              // Title
              const Text(
                '❌ Đáp án sai!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 12),

              // Message
              const Text(
                'Bạn có muốn thử lại hay xem đáp án đúng?',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        // Reset để thử lại
                        _generateChoices();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Thử lại',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        // Reset combo khi xem đáp án
                        provider.resetCombo();

                        // Hiển thị đáp án đúng
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Đáp án đúng là: ${provider.currentItem.meaning}\nCombo đã được reset!',
                            ),
                            backgroundColor: Colors.orange,
                            duration: const Duration(seconds: 3),
                          ),
                        );

                        // Reset sau 3 giây
                        Future.delayed(const Duration(seconds: 3), () {
                          if (mounted) {
                            _generateChoices();
                          }
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Hiển thị đáp án',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VocabularyProvider>(
      builder: (context, provider, child) {
        final item = provider.currentItem;

        // Regenerate choices when item changes
        if (_choices.isEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _generateChoices();
          });
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Combo display with animation
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: provider.currentCombo > 0
                      ? Colors.orange[100]
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color:
                        provider.currentCombo > 0 ? Colors.orange : Colors.grey,
                    width: 2,
                  ),
                  boxShadow: provider.currentCombo > 0
                      ? [
                          BoxShadow(
                            color: Colors.orange.withValues(alpha: 0.3),
                            blurRadius: 8,
                            spreadRadius: 2,
                          )
                        ]
                      : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: Icon(
                        Icons.local_fire_department,
                        key: ValueKey(provider.currentCombo),
                        color: provider.currentCombo > 0
                            ? Colors.orange
                            : Colors.grey,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 8),
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: Text(
                        'Combo: ${provider.currentCombo}',
                        key: ValueKey(provider.currentCombo),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: provider.currentCombo > 0
                              ? Colors.orange[800]
                              : Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Text(
                item.word,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                item.pronunciation,
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
              const SizedBox(height: 40),
              Expanded(
                child: ListView.builder(
                  itemCount: _choices.length,
                  itemBuilder: (context, index) {
                    final choice = _choices[index];
                    final isSelected = _selectedAnswer == choice;
                    final isCorrect = choice == item.meaning;

                    Color backgroundColor = Colors.grey[200]!;
                    if (_showResult && isSelected) {
                      backgroundColor =
                          isCorrect ? Colors.green[100]! : Colors.red[100]!;
                    } else if (isSelected) {
                      backgroundColor = Colors.blue[100]!;
                    }

                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: ElevatedButton(
                        onPressed: () => _selectAnswer(choice),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: backgroundColor,
                          foregroundColor: Colors.black87,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          choice,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 24),
              // Check/Next button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _selectedAnswer == null
                      ? null
                      : (_showResult
                          ? null // Không cần nút khi đã show result
                          : _checkAnswer),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _showResult && _selectedAnswer == item.meaning
                            ? Colors.green
                            : Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: _selectedAnswer == null ? 0 : 2,
                  ),
                  child: _showResult && _selectedAnswer == item.meaning
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Đang chuyển...',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        )
                      : Text(
                          _selectedAnswer == null
                              ? 'Chọn đáp án để tiếp tục'
                              : (_showResult ? 'Đã sai' : 'Kiểm tra đáp án'),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
