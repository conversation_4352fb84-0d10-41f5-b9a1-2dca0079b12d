#!/usr/bin/env python3
# convert_json.py - <PERSON>ript chuyển đổi JSON array thành Firestore format

import json
import re
from datetime import datetime

def load_vocabulary_from_file(file_path):
    """Load vocabulary data from JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return []

# Load dữ liệu từ file thực tế
original_data = load_vocabulary_from_file('assets/data/vocabulary.json')

if not original_data:
    print("❌ Không thể load dữ liệu từ file!")
    exit(1)

print(f"📊 Đã load {len(original_data)} từ vựng từ file")

# Dữ liệu mẫu để test (nếu file không load được)
sample_data = [
    {
        "word": "aged",
        "part_of_speech": "adj.",
        "level": "B1",
        "vietnamese_meaning": "già đi",
        "examples": [
            {
                "english": "Grief ages us.",
                "vietnamese": "<PERSON>au buồn làm chúng ta già đi."
            },
            {
                "english": "Money's a little tight right now, let's age our bills for a week or so.",
                "vietnamese": "Hiện tại tiền hơi eo hẹp, hãy để hóa đơn của chúng tôi trong một tuần hoặc lâu hơn."
            }
        ],
        "antonyms": []
    },
    {
        "word": "agency",
        "part_of_speech": "n.",
        "level": "B2",
        "vietnamese_meaning": "tác dụng, lực; môi giới, trung gian",
        "examples": [
            {
                "english": "individual agency",
                "vietnamese": "cơ quan cá nhân"
            },
            {
                "english": "authority of agency",
                "vietnamese": "thẩm quyền của cơ quan"
            }
        ],
        "antonyms": []
    },
    {
        "word": "agenda",
        "part_of_speech": "n.",
        "level": "B2",
        "vietnamese_meaning": "chương trình nghị sự",
        "examples": [
            {
                "english": "The agenda is very important.",
                "vietnamese": "Chương trình nghị sự rất quan trọng."
            },
            {
                "english": "I need a new agenda.",
                "vietnamese": "Tôi cần một chương trình nghị sự mới."
            }
        ],
        "antonyms": []
    },
    {
        "word": "agent",
        "part_of_speech": "n.",
        "level": "B1",
        "vietnamese_meaning": "đại lý, tác nhân",
        "examples": [
            {
                "english": "The agent is very important.",
                "vietnamese": "Người đại diện rất quan trọng."
            },
            {
                "english": "I need a new agent.",
                "vietnamese": "Tôi cần một đặc vụ mới."
            }
        ],
        "antonyms": []
    }
]

def generate_search_keywords(vocabulary):
    """Tạo search keywords cho từ vựng"""
    keywords = set()
    
    # Add word variations
    keywords.add(vocabulary['word'].lower())
    keywords.add(vocabulary['vietnamese_meaning'].lower())
    
    # Add word parts (for partial search)
    word = vocabulary['word']
    for i in range(1, len(word) + 1):
        keywords.add(word[:i].lower())
    
    # Add meaning parts
    meaning_words = vocabulary['vietnamese_meaning'].lower().split()
    keywords.update(meaning_words)
    
    # Add examples
    for example in vocabulary['examples']:
        english_words = re.findall(r'\w+', example['english'].lower())
        vietnamese_words = re.findall(r'\w+', example['vietnamese'].lower())
        keywords.update(english_words)
        keywords.update(vietnamese_words)
    
    # Filter out empty strings and very short words
    return [keyword for keyword in keywords if len(keyword) > 0]

def convert_to_firestore_format(data):
    """Chuyển đổi array JSON thành Firestore format"""
    firestore_data = {
        "vocabularies": {}
    }
    
    current_time = datetime.now().isoformat() + "Z"
    
    for index, item in enumerate(data):
        # Tạo document ID unique
        doc_id = f"{item['word'].lower()}_{str(index + 1).zfill(3)}"
        
        # Thêm metadata
        firestore_item = {
            **item,
            "searchKeywords": generate_search_keywords(item),
            "createdAt": current_time,
            "updatedAt": current_time
        }
        
        firestore_data["vocabularies"][doc_id] = firestore_item
    
    return firestore_data

def main():
    """Main function"""
    print("🔄 Đang chuyển đổi JSON...")

    # Sử dụng dữ liệu thực tế hoặc sample data
    data_to_convert = original_data if original_data else sample_data

    # Chuyển đổi dữ liệu
    firestore_data = convert_to_firestore_format(data_to_convert)

    # Ghi file output với format Firestore import
    output_data = {
        "__collections__": firestore_data["vocabularies"]
    }

    with open('firestore_import_data.json', 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    print("✅ Đã chuyển đổi thành công!")
    print(f"📊 Số từ vựng: {len(firestore_data['vocabularies'])}")
    print("📁 File output: firestore_import_data.json")

    # Hiển thị cấu trúc mẫu
    print("\n📋 Cấu trúc mẫu:")
    first_key = list(firestore_data["vocabularies"].keys())[0]
    sample_doc = firestore_data["vocabularies"][first_key]
    print(f"{first_key}:")
    print(json.dumps(sample_doc, ensure_ascii=False, indent=2)[:500] + "...")

    # Thống kê theo level
    print("\n📈 Thống kê theo CEFR level:")
    level_stats = {}
    for doc in firestore_data["vocabularies"].values():
        level = doc.get('level', 'Unknown')
        level_stats[level] = level_stats.get(level, 0) + 1

    for level, count in sorted(level_stats.items()):
        print(f"  {level}: {count} từ")

    print(f"\n🎯 Sẵn sàng import vào Firebase!")
    print("📝 Lệnh import: firebase firestore:import firestore_import_data.json")

if __name__ == "__main__":
    main()
