# Learning English Smart

A comprehensive Flutter application for learning English vocabulary with pronunciation, multiple study modes, and CEFR level classification.

## Features

### 📚 Vocabulary Learning
- **CEFR Level Classification**: Words categorized by A1, A2, B1, B2, C1, C2 levels
- **Comprehensive Dictionary**: Over 100,000 English-Vietnamese word pairs
- **Pronunciation Guide**: Audio pronunciation for each word
- **Multiple Study Modes**:
  - Reading mode with definitions
  - Multiple choice questions
  - Speaking practice
  - Listening exercises

### 🎯 Study Modes
- **Multiple Choice**: Test your vocabulary knowledge
- **Speaking Practice**: Improve pronunciation with speech recognition
- **Progress Tracking**: Monitor your learning progress across different levels
- **Smart Review**: Focus on words you need to practice more

### 🔧 Technical Features
- **Offline Support**: Works without internet connection after initial setup
- **Auto Import**: Automatically downloads and imports dictionary data
- **Level Classifier**: AI-powered word difficulty classification
- **Progress Persistence**: Your progress is saved locally

## Getting Started

### Prerequisites
- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code
- Android device or emulator

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/[your-username]/learning-english-smart.git
   cd learning-english-smart
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

### First Time Setup

1. **Import Dictionary Data**
   - Open the app
   - Go to Vocabulary screen
   - Tap "Import Từ Điển" button
   - Wait for the download and import process to complete

2. **Select Your Level**
   - Choose your CEFR level (A1-C2)
   - Start learning!

## Data Source

The vocabulary data is sourced from:
- [English-Vietnamese Dictionary](https://github.com/manhminno/English-Vietnamese-Dictionary)
- Automatically classified into CEFR levels using custom algorithms

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License.

---

Made with ❤️ using Flutter
