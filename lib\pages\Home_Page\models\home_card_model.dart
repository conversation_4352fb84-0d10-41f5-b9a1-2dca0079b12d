import '../widgets/home_card_widget.dart';

/// This class is used in the [LanguageCardWidget] widget.

// ignore_for_file: must_be_immutable
class HomeCardModel {
  HomeCardModel({
    this.id,
    this.languageName,
    this.description,
    this.imagePath,
  }) {
    id = id ?? '';
    languageName = languageName ?? '';
    description = description ?? '';
    imagePath = imagePath ?? '';
  }

  String? id;
  String? languageName;
  String? description;
  String? imagePath;
}
