class LearningProgress {
  final int? id;
  final int wordId;
  final String userId;
  final int masteryLevel; // 0-5 (0: new, 5: mastered)
  final int correctAnswers;
  final int totalAttempts;
  final DateTime? lastReviewed;
  final DateTime? nextReview;
  final int streakCount;
  final bool isFavorite;

  LearningProgress({
    this.id,
    required this.wordId,
    required this.userId,
    this.masteryLevel = 0,
    this.correctAnswers = 0,
    this.totalAttempts = 0,
    this.lastReviewed,
    this.nextReview,
    this.streakCount = 0,
    this.isFavorite = false,
  });

  factory LearningProgress.fromMap(Map<String, dynamic> map) {
    return LearningProgress(
      id: map['id'],
      wordId: map['word_id'],
      userId: map['user_id'],
      masteryLevel: map['mastery_level'] ?? 0,
      correctAnswers: map['correct_answers'] ?? 0,
      totalAttempts: map['total_attempts'] ?? 0,
      lastReviewed: map['last_reviewed'] != null 
          ? DateTime.parse(map['last_reviewed']) 
          : null,
      nextReview: map['next_review'] != null 
          ? DateTime.parse(map['next_review']) 
          : null,
      streakCount: map['streak_count'] ?? 0,
      isFavorite: (map['is_favorite'] ?? 0) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'word_id': wordId,
      'user_id': userId,
      'mastery_level': masteryLevel,
      'correct_answers': correctAnswers,
      'total_attempts': totalAttempts,
      'last_reviewed': lastReviewed?.toIso8601String(),
      'next_review': nextReview?.toIso8601String(),
      'streak_count': streakCount,
      'is_favorite': isFavorite ? 1 : 0,
    };
  }

  double get accuracyRate {
    if (totalAttempts == 0) return 0.0;
    return correctAnswers / totalAttempts;
  }

  bool get needsReview {
    if (nextReview == null) return true;
    return DateTime.now().isAfter(nextReview!);
  }

  DateTime calculateNextReview() {
    final now = DateTime.now();
    
    // Spaced repetition algorithm
    switch (masteryLevel) {
      case 0: // New word
        return now.add(const Duration(minutes: 10));
      case 1: // Learning
        return now.add(const Duration(hours: 4));
      case 2: // Familiar
        return now.add(const Duration(days: 1));
      case 3: // Known
        return now.add(const Duration(days: 3));
      case 4: // Well known
        return now.add(const Duration(days: 7));
      case 5: // Mastered
        return now.add(const Duration(days: 30));
      default:
        return now.add(const Duration(hours: 1));
    }
  }

  LearningProgress updateAfterAnswer(bool isCorrect) {
    final newCorrectAnswers = isCorrect ? correctAnswers + 1 : correctAnswers;
    final newTotalAttempts = totalAttempts + 1;
    final newStreakCount = isCorrect ? streakCount + 1 : 0;
    
    int newMasteryLevel = masteryLevel;
    if (isCorrect && streakCount >= 2) {
      newMasteryLevel = (masteryLevel + 1).clamp(0, 5);
    } else if (!isCorrect && masteryLevel > 0) {
      newMasteryLevel = (masteryLevel - 1).clamp(0, 5);
    }

    final nextReview = calculateNextReview();

    return LearningProgress(
      id: id,
      wordId: wordId,
      userId: userId,
      masteryLevel: newMasteryLevel,
      correctAnswers: newCorrectAnswers,
      totalAttempts: newTotalAttempts,
      lastReviewed: DateTime.now(),
      nextReview: nextReview,
      streakCount: newStreakCount,
      isFavorite: isFavorite,
    );
  }

  LearningProgress copyWith({
    int? id,
    int? wordId,
    String? userId,
    int? masteryLevel,
    int? correctAnswers,
    int? totalAttempts,
    DateTime? lastReviewed,
    DateTime? nextReview,
    int? streakCount,
    bool? isFavorite,
  }) {
    return LearningProgress(
      id: id ?? this.id,
      wordId: wordId ?? this.wordId,
      userId: userId ?? this.userId,
      masteryLevel: masteryLevel ?? this.masteryLevel,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      totalAttempts: totalAttempts ?? this.totalAttempts,
      lastReviewed: lastReviewed ?? this.lastReviewed,
      nextReview: nextReview ?? this.nextReview,
      streakCount: streakCount ?? this.streakCount,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}
