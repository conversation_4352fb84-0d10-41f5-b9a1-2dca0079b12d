import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:learning_english_smart/screens/vocabulary_screen/prv/vocabulary_provider.dart';
import 'package:learning_english_smart/screens/vocabulary_screen/model/memory_status.dart';
import 'package:provider/provider.dart';
import '../widgets/study_mode_widget.dart';
import '../widgets/multiple_choice_widget.dart';
import '../widgets/speaking_mode_widget.dart';

@RoutePage()
class VocabularyScreen extends StatelessWidget {
  const VocabularyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => VocabularyProvider(),
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Consumer<VocabularyProvider>(
            builder: (context, provider, child) {
              return Column(
                children: [
                  _buildHeader(context, provider),
                  _buildProgressIndicator(context, provider),
                  _buildModeSelector(provider),
                  Expanded(
                    child: _buildContent(provider),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, VocabularyProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => context.router.pop(),
          ),
          const Expanded(
            child: Text(
              'Vocabulary',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(
      BuildContext context, VocabularyProvider provider) {
    final currentLevel = provider.selectedLevel;
    final totalWords = provider.getTotalWordsForLevel();
    final currentProgress = provider.getCurrentProgress();

    // Display name for current level
    String displayLevel = currentLevel;
    if (currentLevel.startsWith('memory_')) {
      final statusKey = currentLevel.substring(7);
      final status = MemoryStatusExtension.fromKey(statusKey);
      displayLevel = status.label;
    }

    return GestureDetector(
      onTap: () => _showLevelSelector(context, provider),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Text(
              'Tiến độ $displayLevel $currentProgress/$totalWords',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(width: 8),
            const Icon(Icons.open_in_new, size: 12, color: Colors.grey),
            const Spacer(),
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLevelSelector(BuildContext context, VocabularyProvider provider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Memory status section
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Học theo trạng thái nhớ',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ),

              // Memory status options
              ...MemoryStatus.values.map((status) {
                final memoryStats = provider.getMemoryStatusStats();
                final count = memoryStats[status.key] ?? 0;

                return ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: status.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: status.color),
                    ),
                    child: Center(
                      child: Text(
                        status.symbol,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: status.color,
                        ),
                      ),
                    ),
                  ),
                  title: Text(status.label),
                  subtitle: Text('$count từ'),
                  trailing: provider.selectedLevel == 'memory_${status.key}'
                      ? const Icon(Icons.check, color: Colors.blue)
                      : null,
                  onTap: () async {
                    Navigator.pop(context);
                    await provider.setMemoryStatusFilter(status);
                  },
                );
              }),

              // Divider
              const Divider(),

              const Text(
                'Chọn cấp độ',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 16),
              ...['A1', 'A2', 'B1', 'B2', 'C1', 'C2'].map((level) {
                final stats = provider.getVocabularyStats();
                final totalForLevel = stats[level] ?? 0;
                final progressForLevel = provider.getProgressForLevel(level);

                return ListTile(
                  title: Text(level),
                  subtitle: Text('$progressForLevel/$totalForLevel từ'),
                  trailing: provider.selectedLevel == level
                      ? const Icon(Icons.check, color: Colors.blue)
                      : null,
                  onTap: () async {
                    Navigator.pop(context);
                    await provider.setSelectedLevel(level);
                  },
                );
              }),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent(VocabularyProvider provider) {
    // Show loading state
    if (provider.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Đang tải từ vựng...'),
          ],
        ),
      );
    }

    // Show error state
    if (provider.errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Có lỗi xảy ra',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              provider.errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: provider.reloadVocabularyData,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    // Show empty state
    if (!provider.hasVocabularyData) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Không có từ vựng',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Hãy thử thay đổi bộ lọc hoặc từ khóa tìm kiếm',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    // Show content based on mode
    switch (provider.currentMode) {
      case LearningMode.study:
        return const StudyModeWidget();
      case LearningMode.multipleChoice:
        return const MultipleChoiceWidget();
      case LearningMode.speaking:
        return const SpeakingModeWidget();
    }
  }

  Widget _buildModeSelector(VocabularyProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildModeTab('Direct\nStudy', LearningMode.study, provider),
          _buildModeTab(
              'Multiple\nChoice', LearningMode.multipleChoice, provider),
          _buildModeTab('Concealed\nPanel', LearningMode.speaking, provider),
        ],
      ),
    );
  }

  Widget _buildModeTab(
      String title, LearningMode mode, VocabularyProvider provider) {
    final isSelected = provider.currentMode == mode;
    return Expanded(
      child: GestureDetector(
        onTap: () => provider.setMode(mode),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isSelected ? Colors.blue : Colors.transparent,
                width: 2,
              ),
            ),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              color: isSelected ? Colors.blue : Colors.grey[600],
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}
