import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_image_view.dart';
import '../models/home_card_model.dart';

class HomeCardWidget extends StatelessWidget {
  final HomeCardModel languageCard;
  final VoidCallback? onTap;

  const HomeCardWidget({
    Key? key,
    required this.languageCard,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Sized<PERSON><PERSON>(
        width: 160.h,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomImageView(
              imagePath: languageCard.imagePath ?? '',
              height: 160.h,
              width: 160.h,
              fit: BoxFit.cover,
            ),
            Sized<PERSON><PERSON>(height: 16.h),
            Text(
              languageCard.languageName ?? '',
              style: TextStyleHelper.instance.title16Medium,
            ),
            Si<PERSON><PERSON><PERSON>(height: 4.h),
            Text(
              languageCard.description ?? '',
              style: TextStyleHelper.instance.body14Regular,
            ),
          ],
        ),
      ),
    );
  }
}
