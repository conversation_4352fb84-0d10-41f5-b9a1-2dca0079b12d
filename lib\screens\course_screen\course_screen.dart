import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:learning_english_smart/routes/app_router.dart';
import '../../core/app_export.dart';
import '../../widgets/custom_bottom_navigation_bar.dart';

@RoutePage()
class CourseScreen extends StatefulWidget {
  const CourseScreen({super.key});

  @override
  State<CourseScreen> createState() => _CourseScreenState();
}

class _CourseScreenState extends State<CourseScreen> {
  int currentBottomIndex = 1; // Lesson tab

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bài học'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Text(
              'Kh<PERSON><PERSON> học của bạn',
              style: TextStyle(
                fontSize: 24.fSize,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20.h),

            // Course Progress Card
            _buildCourseProgressCard(),
            SizedBox(height: 24.h),

            // Lessons List
            Text(
              'Danh sách bài học',
              style: TextStyle(
                fontSize: 20.fSize,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),

            // Lesson Items
            _buildLessonItem('Bài 1: Giới thiệu bản thân', 'Hoàn thành', true),
            _buildLessonItem('Bài 2: Gia đình và bạn bè', 'Đang học', false),
            _buildLessonItem(
                'Bài 3: Thời gian và ngày tháng', 'Chưa học', false),
            _buildLessonItem('Bài 4: Mua sắm và thanh toán', 'Chưa học', false),
            _buildLessonItem('Bài 5: Du lịch và giao thông', 'Chưa học', false),
          ],
        ),
      ),
    );
  }

  Widget _buildCourseProgressCard() {
    return Container(
      padding: EdgeInsets.all(20.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[400]!, Colors.blue[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.h),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tiến độ học tập',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18.fSize,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '2/5 bài học',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.fSize,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    LinearProgressIndicator(
                      value: 0.4,
                      backgroundColor: Colors.white.withOpacity(0.3),
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.h),
              CircleAvatar(
                radius: 25.h,
                backgroundColor: Colors.white.withOpacity(0.2),
                child: Text(
                  '40%',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.fSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLessonItem(String title, String status, bool isCompleted) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.h),
        border: Border.all(
          color: isCompleted ? Colors.green : Colors.grey[300]!,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40.h,
            height: 40.h,
            decoration: BoxDecoration(
              color: isCompleted ? Colors.green : Colors.grey[300],
              shape: BoxShape.circle,
            ),
            child: Icon(
              isCompleted ? Icons.check : Icons.play_arrow,
              color: Colors.white,
              size: 20.h,
            ),
          ),
          SizedBox(width: 16.h),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.fSize,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  status,
                  style: TextStyle(
                    fontSize: 14.fSize,
                    color: isCompleted ? Colors.green : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: Colors.grey[400],
            size: 16.h,
          ),
        ],
      ),
    );
  }
}
