// import_to_firebase.js - Script import dữ liệu vào Firebase
const admin = require('firebase-admin');
const fs = require('fs');

// Khởi tạo Firebase Admin SDK
// Bạn cần tải service account key từ Firebase Console
const serviceAccount = require('./serviceAccountKey.json'); // Tải từ Firebase Console

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'learning-english-smart' // Thay bằng project ID của bạn
});

const db = admin.firestore();

async function importData() {
  try {
    console.log('🔄 Bắt đầu import dữ liệu...');
    
    // Đọc file JSON
    const data = JSON.parse(fs.readFileSync('firestore_import.json', 'utf8'));
    const vocabularies = data.__collections__.vocabularies;
    
    // Batch write để import nhanh hơn
    const batch = db.batch();
    let count = 0;
    
    for (const [docId, docData] of Object.entries(vocabularies)) {
      const docRef = db.collection('vocabularies').doc(docId);
      batch.set(docRef, docData);
      count++;
      
      // Commit batch mỗi 500 documents (giới hạn của Firestore)
      if (count % 500 === 0) {
        await batch.commit();
        console.log(`✅ Đã import ${count} documents`);
      }
    }
    
    // Commit batch cuối cùng
    if (count % 500 !== 0) {
      await batch.commit();
    }
    
    console.log(`🎉 Import hoàn tất! Tổng cộng: ${count} từ vựng`);
    
    // Verify import
    const snapshot = await db.collection('vocabularies').limit(5).get();
    console.log('\n📋 Dữ liệu mẫu đã import:');
    snapshot.forEach(doc => {
      console.log(`- ${doc.id}: ${doc.data().word} (${doc.data().level})`);
    });
    
  } catch (error) {
    console.error('❌ Lỗi import:', error);
  } finally {
    process.exit();
  }
}

// Chạy import
importData();
