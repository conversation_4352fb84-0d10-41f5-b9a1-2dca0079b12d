import 'package:flutter/material.dart';
import '../model/chat_message.dart';

class ChatProvider with ChangeNotifier {
  final List<ChatMessage> _messages = [];
  bool _isTyping = false;
  bool _isAiResponding = false;

  // Getters
  List<ChatMessage> get messages => _messages;
  bool get isTyping => _isTyping;
  bool get isAiResponding => _isAiResponding;

  ChatProvider() {
    _initializeChat();
  }

  void _initializeChat() {
    _messages.addAll([
      ChatMessage(
        id: '1',
        content:
            "Hi there! I'm your AI English tutor. Ask me anything, and I'll help you improve your English.",
        isFromUser: false,
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        senderName: 'AI Tutor',
      ),
      ChatMessage(
        id: '2',
        content: "Hello!",
        isFromUser: true,
        timestamp: DateTime.now().subtract(const Duration(minutes: 4)),
      ),
      ChatMessage(
        id: '3',
        content:
            "Of course, <PERSON>! I'm here to help. What specific grammar points are you struggling with?",
        isFromUser: false,
        timestamp: DateTime.now().subtract(const Duration(minutes: 3)),
        senderName: 'AI Tutor',
      ),
      ChatMessage(
        id: '4',
        content: "I often confuse 'their', 'there', and 'they're'.",
        isFromUser: true,
        timestamp: DateTime.now().subtract(const Duration(minutes: 2)),
      ),
      ChatMessage(
        id: '5',
        content: """That's a common mistake! Let's break it down:

- "Their" shows possession (e.g., "Their car is red.")
- "There" indicates a place (e.g., "The book is over there.")
- "They're" is a contraction of "they are" (e.g., "They're going to the park.")

Try using each in a sentence, and I'll provide feedback.""",
        isFromUser: false,
        timestamp: DateTime.now().subtract(const Duration(minutes: 1)),
        senderName: 'AI Tutor',
      ),
    ]);
  }

  void sendMessage(String content) {
    if (content.trim().isEmpty) return;

    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content.trim(),
      isFromUser: true,
      timestamp: DateTime.now(),
    );

    _messages.add(message);
    notifyListeners();

    // Simulate AI response
    _simulateAiResponse();
  }

  void _simulateAiResponse() {
    _isAiResponding = true;
    notifyListeners();

    Future.delayed(const Duration(seconds: 2), () {
      final responses = [
        "Great question! Let me help you with that...",
        "That's an excellent point. Here's what I think...",
        "I understand your confusion. Let me explain...",
        "Good observation! Here's how you can improve...",
        "Perfect! You're making great progress. Let's continue...",
      ];

      final randomResponse =
          responses[DateTime.now().millisecond % responses.length];

      _messages.add(
        ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: randomResponse,
          isFromUser: false,
          timestamp: DateTime.now(),
          senderName: 'AI Tutor',
        ),
      );

      _isAiResponding = false;
      notifyListeners();
    });
  }

  void clearChat() {
    _messages.clear();
    _initializeChat();
    notifyListeners();
  }

  void deleteMessage(String messageId) {
    _messages.removeWhere((message) => message.id == messageId);
    notifyListeners();
  }
}
