class ChatMessage {
  final String id;
  final String content;
  final bool isFromUser;
  final DateTime timestamp;
  final String? senderName;
  final String? senderAvatar;

  ChatMessage({
    required this.id,
    required this.content,
    required this.isFromUser,
    required this.timestamp,
    this.senderName,
    this.senderAvatar,
  });
}

class PracticeUser {
  final String id;
  final String name;
  final String level;
  final String avatar;
  late final bool isOnline;

  PracticeUser({
    required this.id,
    required this.name,
    required this.level,
    required this.avatar,
    this.isOnline = false,
  });
}

class ConversationStarter {
  final String id;
  final String question;

  ConversationStarter({
    required this.id,
    required this.question,
  });
}
