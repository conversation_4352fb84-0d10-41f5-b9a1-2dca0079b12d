import 'package:flutter/material.dart';

enum MemoryStatus {
  unknown, // ? - <PERSON><PERSON><PERSON> nhớ
  temporary, // ! - <PERSON><PERSON><PERSON> nhớ
  memorized, // ✓ - <PERSON><PERSON> nhớ
}

extension MemoryStatusExtension on MemoryStatus {
  String get symbol {
    switch (this) {
      case MemoryStatus.unknown:
        return '?';
      case MemoryStatus.temporary:
        return '!';
      case MemoryStatus.memorized:
        return '✓';
    }
  }

  String get label {
    switch (this) {
      case MemoryStatus.unknown:
        return 'Chưa nhớ';
      case MemoryStatus.temporary:
        return 'Tạm nhớ';
      case MemoryStatus.memorized:
        return 'Đã nhớ';
    }
  }

  Color get color {
    switch (this) {
      case MemoryStatus.unknown:
        return Colors.grey;
      case MemoryStatus.temporary:
        return Colors.orange;
      case MemoryStatus.memorized:
        return Colors.green;
    }
  }

  String get key {
    switch (this) {
      case MemoryStatus.unknown:
        return 'unknown';
      case MemoryStatus.temporary:
        return 'temporary';
      case MemoryStatus.memorized:
        return 'memorized';
    }
  }

  static MemoryStatus from<PERSON><PERSON>(String key) {
    switch (key) {
      case 'unknown':
        return MemoryStatus.unknown;
      case 'temporary':
        return MemoryStatus.temporary;
      case 'memorized':
        return MemoryStatus.memorized;
      default:
        return MemoryStatus.unknown;
    }
  }
}
