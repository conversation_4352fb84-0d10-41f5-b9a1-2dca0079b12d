import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../core/utils/image_constant.dart';
import '../models/home_card_model.dart';
import '../models/home_learning_dashboard_model.dart';

class HomePrv extends ChangeNotifier {
  HomeLearningDashboardModel languageLearningDashboardModel =
      HomeLearningDashboardModel();
  List<HomeCardModel> languageCards = [];
  int selectedBottomIndex = 0;

  HomePrv() {
    _initializeData();
  }

  void _initializeData() {
    languageLearningDashboardModel = HomeLearningDashboardModel(
      currentXP: 60,
      targetXP: 100,
    );

    languageCards = [
      HomeCardModel(
        id: '1',
        languageName: 'Spanish',
        description: 'Continue learning',
        imagePath: ImageConstant.imgDepth5Frame0,
      ),
      HomeCardModel(
        id: '2',
        languageName: 'French',
        description: 'Continue learning',
        imagePath: ImageConstant.imgDepth5Frame0160x160,
      ),
      HomeCardModel(
        id: '3',
        languageName: 'German',
        description: 'Continue learning',
        imagePath: ImageConstant.imgDepth5Frame01,
      ),
    ];
  }

  void initialize() {
    _initializeData();
    notifyListeners(); // Thông báo cho UI cập nhật
  }

  void onSettingsTap() {
    // Handle settings navigation
  }

  void onLanguageCardTap(HomeCardModel languageCard) {
    // Handle language card tap navigation
  }

  void onPracticeTap() {
    // Handle practice navigation
  }

  void onStoriesTap() {
    // Handle stories navigation
  }

  void onBottomNavigationChanged(int index) {
    selectedBottomIndex = index;
    notifyListeners();

    // Handle bottom navigation based on index
    switch (index) {
      case 0:
        // Already on Home tab
        break;
      case 1:
        // Navigate to Lesson
        break;
      case 2:
        // Navigate to Friends
        break;
      case 3:
        // Navigate to Account
        break;
    }
  }
}
