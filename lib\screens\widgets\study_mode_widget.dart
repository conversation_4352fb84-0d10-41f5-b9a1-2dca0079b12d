import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:learning_english_smart/core/constants/constants.dart';
import 'package:learning_english_smart/widgets/primary_card.dart';
import 'package:provider/provider.dart';
import '../vocabulary_screen/prv/vocabulary_provider.dart';
import '../vocabulary_screen/model/vocabulary_item.dart';
import '../vocabulary_screen/model/memory_status.dart';

class StudyModeWidget extends StatefulWidget {
  final bool showNextButton;
  final VoidCallback? onNext;
  final VocabularyItem? fixedItem; // Từ cố định để hiển thị

  const StudyModeWidget({
    super.key,
    this.showNextButton = false,
    this.onNext,
    this.fixedItem,
  });

  @override
  State<StudyModeWidget> createState() => _StudyModeWidgetState();
}

class _StudyModeWidgetState extends State<StudyModeWidget> {
  bool _isPressed = false;

  void _handleTap() {
    setState(() {
      _isPressed = true;
    });

    // Tự động reset màu sau 200ms
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _isPressed = false;
        });
      }
    });
  }

  Widget _buildMemoryStatusButton(
    VocabularyProvider provider,
    MemoryStatus status,
    String symbol,
    Color defaultColor,
  ) {
    final currentStatus = provider.getCurrentWordMemoryStatus();
    final isSelected = currentStatus == status;

    return GestureDetector(
      onTap: () {
        provider.setCurrentWordMemoryStatus(status);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(8),
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isSelected ? status.color : defaultColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                isSelected ? status.color.withValues(alpha: 0.8) : Colors.grey,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: status.color.withValues(alpha: 0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  )
                ]
              : null,
        ),
        child: Center(
          child: Text(
            symbol,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isSelected ? Colors.white : Colors.black87,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VocabularyProvider>(
      builder: (context, provider, child) {
        final item = widget.fixedItem ?? provider.currentItem;

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Expanded(
                child: PrimaryCard(
                  padding: const EdgeInsets.all(16),
                  // elevation: 2,
                  // shape: RoundedRectangleBorder(
                  //   borderRadius: BorderRadius.circular(12),
                  // ),
                  child: Column(
                    children: [
                      const SizedBox(height: 16),
                      // Word and pronunciation section
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Word and pronunciation
                          Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: _handleTap,
                                  child: Text(
                                    item.word,
                                    style: TextStyle(
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      color: _isPressed
                                          ? Colors.blue
                                          : Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.volume_up),
                                color: _isPressed ? Colors.blue : Colors.black,
                                onPressed: () {
                                  // Xử lý khi click icon
                                },
                              ),
                            ],
                          ),
                          // Pronunciation
                          Text(
                            item.pronunciation,
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  _isPressed ? Colors.blue : Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // Meaning
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          item.meaning,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      // Image container - make it flexible
                      Expanded(
                        flex: 3,
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.orange[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.person,
                              size: 60,
                              color: Colors.teal,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Example container - make it flexible
                      Expanded(
                        flex: 2,
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    const Icon(Icons.chat_bubble_outline,
                                        size: 16),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        item.example,
                                        style: const TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 5),
                                // Row(
                                //   children: [
                                //     const SizedBox(width: 25),
                                //     Expanded(
                                //       child: Text(
                                //         'Ví dụ: Hôm này trời đẹp quá. Xin chào các bạn. Tôi là một người yêu thích học tiếng Anh.',
                                //         style: TextStyle(
                                //           fontSize: 12,
                                //           color: Colors.grey[600],
                                //         ),
                                //       ),
                                //     ),
                                //   ],
                                // ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          PrimaryCard(
                            width: 100.w,
                            background: grayScaleColor5,
                            padding: const EdgeInsets.all(8),
                            borderRadius: BorderRadius.circular(15),
                            child: Row(
                              children: [
                                const Icon(Icons.mic, size: 16),
                                const SizedBox(width: 4),
                                const Text(
                                  'Record & Check',
                                  style: TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'Trái nghĩa',
                            style: TextStyle(
                              fontSize: 10,
                              // color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Text(
                            'bye <-> tạm biệt',
                            style: TextStyle(
                              fontSize: 10,
                              // color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Text(
                            'Good bye <-> tạm biệt',
                            style: TextStyle(
                              fontSize: 10,
                              // color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                      vpad(20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildMemoryStatusButton(
                            provider,
                            MemoryStatus.unknown,
                            '?',
                            Colors.grey[300]!,
                          ),
                          const SizedBox(width: 8),
                          _buildMemoryStatusButton(
                            provider,
                            MemoryStatus.temporary,
                            '!',
                            Colors.orange[300]!,
                          ),
                          const SizedBox(width: 8),
                          _buildMemoryStatusButton(
                            provider,
                            MemoryStatus.memorized,
                            '✓',
                            Colors.green[300]!,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Navigation buttons
              widget.showNextButton
                  ? SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: widget.onNext,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Kế tiếp',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: provider.currentIndex > 0
                                ? () {
                                    provider.previousItem();
                                  }
                                : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[600],
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Trở lại'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: provider.currentIndex <
                                    provider.vocabularyItems.length - 1
                                ? () {
                                    provider.nextItem();
                                  }
                                : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[600],
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Học tiếp'),
                          ),
                        ),
                      ],
                    ),
            ],
          ),
        );
      },
    );
  }
}
