import 'package:flutter/material.dart';
import 'package:learning_english_smart/pages/Learn_Page/models/learn_item_model.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_icon_button.dart';

class LearnItemWidget extends StatelessWidget {
  final LearnItemModel lessonItem;
  final VoidCallback? onTap;

  const LearnItemWidget({
    Key? key,
    required this.lessonItem,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 12.h),
        child: Row(
          children: [
            CustomIconButton(
              iconPath:
                  lessonItem.iconPath ?? ImageConstant.imgDepth3Frame0Gray90001,
              width: 48.h,
              height: 48.h,
              backgroundColor: appTheme.colorFFF2F2,
              borderRadius: 8.h,
              iconSize: 24.h,
            ),
            SizedBox(width: 16.h),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    lessonItem.title ?? '',
                    style: TextStyleHelper.instance.title16Medium
                        .copyWith(color: appTheme.colorFF1114),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    lessonItem.description ?? '',
                    style: TextStyleHelper.instance.body14Regular
                        .copyWith(color: appTheme.colorFF667A),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
