import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../vocabulary_screen/prv/vocabulary_provider.dart';
import '../widgets/translation_widget.dart';
import '../widgets/listening_widget.dart';
import '../widgets/word_order_widget.dart';
import '../widgets/completion_widget.dart';

@RoutePage()
class ExerciseScreen extends StatelessWidget {
  const ExerciseScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Consumer<VocabularyProvider>(
          builder: (context, provider, child) {
            return Column(
              children: [
                _buildHeader(context),
                _buildProgressBar(),
                Expanded(
                  child: _buildExerciseContent(provider),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
          const Spacer(),
          Text(
            '2/10',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: LinearProgressIndicator(
        value: 0.2,
        backgroundColor: Colors.grey[300],
        valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
      ),
    );
  }

  Widget _buildExerciseContent(VocabularyProvider provider) {
    if (provider.isExerciseCompleted) {
      return const CompletionWidget();
    }

    switch (provider.currentExerciseType) {
      case ExerciseType.translation:
        return const TranslationWidget();
      case ExerciseType.listening:
        return const ListeningWidget();
      case ExerciseType.wordOrder:
        return const WordOrderWidget();
      default:
        return const TranslationWidget();
    }
  }
}
