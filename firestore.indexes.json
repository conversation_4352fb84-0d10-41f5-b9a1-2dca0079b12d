{"indexes": [{"collectionGroup": "vocabularies", "queryScope": "COLLECTION", "fields": [{"fieldPath": "level", "order": "ASCENDING"}, {"fieldPath": "word", "order": "ASCENDING"}]}, {"collectionGroup": "vocabularies", "queryScope": "COLLECTION", "fields": [{"fieldPath": "searchKeywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "level", "order": "ASCENDING"}]}, {"collectionGroup": "vocabularies", "queryScope": "COLLECTION", "fields": [{"fieldPath": "level", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}