"""
VOCABULARY JSON TO FIRESTORE CONVERTER - PHIÊN BẢN HOÀN CHỈNH
Chạy trên Google Colab để convert và import trực tiếp vào Firebase

Tính năng:
✅ Sửa lỗi JSON tự động
✅ Convert sang format Firestore
✅ Import trực tiếp vào Firebase (không cần CLI)
✅ Tạo file backup để download
✅ Thống kê chi tiết

Hướng dẫn sử dụng:
1. Upload file vocabulary.json lên Colab
2. Nhập Firebase config
3. Chạy script này
4. Tự động import vào Firestore
"""

import json
import re
from datetime import datetime
from google.colab import files
import zipfile
import os

# Cài đặt Firebase Admin SDK
try:
    import firebase_admin
    from firebase_admin import credentials, firestore
    print("✅ Firebase Admin SDK đã có sẵn")
except ImportError:
    print("📦 Đang cài đặt Firebase Admin SDK...")
    os.system("pip install firebase-admin")
    import firebase_admin
    from firebase_admin import credentials, firestore
    print("✅ Đã cài đặt Firebase Admin SDK")

def upload_vocabulary_file():
    """Upload file vocabulary.json từ máy tính"""
    print("📁 Vui lòng upload file vocabulary.json của bạn:")
    uploaded = files.upload()
    
    # Tìm file JSON đầu tiên
    json_file = None
    for filename in uploaded.keys():
        if filename.endswith('.json'):
            json_file = filename
            break
    
    if not json_file:
        raise Exception("❌ Không tìm thấy file JSON nào!")
    
    print(f"✅ Đã upload file: {json_file}")
    return json_file

def fix_json_content(content):
    """Sửa các lỗi JSON phổ biến"""
    print("🔧 Đang sửa lỗi JSON...")

    # Loại bỏ BOM nếu có
    if content.startswith('\ufeff'):
        content = content[1:]

    # Loại bỏ các ký tự không in được
    content = ''.join(char for char in content if ord(char) >= 32 or char in '\n\r\t')

    # Sửa trailing comma trước }
    content = re.sub(r',(\s*})', r'\1', content)
    content = re.sub(r',(\s*])', r'\1', content)

    # Đảm bảo file kết thúc đúng cách
    content = content.strip()
    if not content.endswith(']') and not content.endswith('}'):
        # Tìm vị trí cuối cùng có thể
        last_brace = content.rfind('}')
        if last_brace > 0:
            content = content[:last_brace + 1] + ']'

    return content

def load_vocabulary_data(file_path):
    """Load dữ liệu từ vựng từ file JSON với xử lý lỗi"""
    try:
        # Đọc file as text trước
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        print(f"📄 Kích thước file: {len(content):,} ký tự")

        # Thử parse JSON trực tiếp trước
        try:
            data = json.loads(content)
            print(f"✅ JSON hợp lệ! Đã load {len(data)} từ vựng")
            return data
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON có lỗi tại dòng {e.lineno}, cột {e.colno}: {e.msg}")
            print("� Đang thử sửa lỗi tự động...")

            # Sửa lỗi JSON
            fixed_content = fix_json_content(content)

            try:
                data = json.loads(fixed_content)
                print(f"✅ Đã sửa lỗi thành công! Load được {len(data)} từ vựng")
                return data
            except json.JSONDecodeError as e2:
                print(f"❌ Vẫn có lỗi sau khi sửa: {e2}")

                # Thử phương pháp cuối cùng: đọc từng dòng
                return load_vocabulary_line_by_line(content)

    except Exception as e:
        print(f"❌ Lỗi khi đọc file: {e}")
        return []

def load_vocabulary_line_by_line(content):
    """Load JSON bằng cách đọc từng object một"""
    print("🔄 Đang thử đọc từng object JSON...")

    vocabularies = []
    lines = content.split('\n')
    current_object = ""
    brace_count = 0

    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if not line or line.startswith('//'):
            continue

        current_object += line + " "

        # Đếm dấu ngoặc
        brace_count += line.count('{') - line.count('}')

        # Nếu object hoàn chỉnh
        if brace_count == 0 and current_object.strip():
            try:
                # Loại bỏ dấu phẩy cuối nếu có
                obj_str = current_object.strip().rstrip(',')

                # Thử parse object
                if obj_str.startswith('{') and obj_str.endswith('}'):
                    obj = json.loads(obj_str)
                    if 'word' in obj:  # Kiểm tra có phải vocabulary object
                        vocabularies.append(obj)

                        if len(vocabularies) % 1000 == 0:
                            print(f"   Đã đọc {len(vocabularies)} từ vựng...")

            except json.JSONDecodeError:
                pass  # Bỏ qua object lỗi

            current_object = ""
            brace_count = 0

    print(f"✅ Đọc thành công {len(vocabularies)} từ vựng từ file bị lỗi")
    return vocabularies

def generate_search_keywords(vocabulary):
    """Tạo search keywords cho từ vựng"""
    keywords = set()
    
    # Add word variations
    word = vocabulary.get('word', '').lower()
    meaning = vocabulary.get('vietnamese_meaning', '').lower()
    
    keywords.add(word)
    keywords.add(meaning)
    
    # Add word parts (for partial search)
    for i in range(1, min(len(word) + 1, 10)):  # Giới hạn để tránh quá nhiều keywords
        keywords.add(word[:i])
    
    # Add meaning parts
    meaning_words = re.findall(r'\w+', meaning)
    keywords.update(meaning_words)
    
    # Add examples
    examples = vocabulary.get('examples', [])
    for example in examples[:3]:  # Chỉ lấy 3 ví dụ đầu để tránh quá nhiều keywords
        if isinstance(example, dict):
            english_words = re.findall(r'\w+', example.get('english', '').lower())
            vietnamese_words = re.findall(r'\w+', example.get('vietnamese', '').lower())
            keywords.update(english_words[:5])  # Giới hạn số từ
            keywords.update(vietnamese_words[:5])
    
    # Filter out empty strings and very short words
    return [keyword for keyword in keywords if len(keyword) > 1]

def create_document_id(word, index):
    """Tạo document ID unique"""
    # Loại bỏ ký tự đặc biệt và tạo ID
    clean_word = re.sub(r'[^\w]', '', word.lower())
    return f"{clean_word}_{str(index + 1).zfill(4)}"

def convert_to_firestore_format(vocabulary_data):
    """Chuyển đổi array JSON thành Firestore format"""
    firestore_collections = {
        "vocabularies": {}
    }
    
    current_time = datetime.now().isoformat() + "Z"
    
    print("🔄 Đang chuyển đổi dữ liệu...")
    
    for index, item in enumerate(vocabulary_data):
        if index % 1000 == 0:
            print(f"   Đã xử lý {index}/{len(vocabulary_data)} từ...")
        
        # Tạo document ID
        word = item.get('word', f'word_{index}')
        doc_id = create_document_id(word, index)
        
        # Tạo document data
        firestore_item = {
            "word": item.get('word', ''),
            "part_of_speech": item.get('part_of_speech', ''),
            "level": item.get('level', 'A1'),
            "vietnamese_meaning": item.get('vietnamese_meaning', ''),
            "examples": item.get('examples', []),
            "antonyms": item.get('antonyms', []),
            "searchKeywords": generate_search_keywords(item),
            "createdAt": current_time,
            "updatedAt": current_time
        }
        
        firestore_collections["vocabularies"][doc_id] = firestore_item
    
    return firestore_collections

def create_firestore_import_file(firestore_data):
    """Tạo file import cho Firestore"""
    # Format cho Firebase CLI import
    import_data = {
        "__collections__": firestore_data
    }
    
    # Ghi file
    output_filename = 'firestore_import.json'
    with open(output_filename, 'w', encoding='utf-8') as f:
        json.dump(import_data, f, ensure_ascii=False, indent=2)
    
    return output_filename

def show_statistics(firestore_data):
    """Hiển thị thống kê dữ liệu"""
    vocabularies = firestore_data["vocabularies"]
    total_count = len(vocabularies)
    
    print(f"\n📈 THỐNG KÊ DỮ LIỆU:")
    print(f"📊 Tổng số từ vựng: {total_count}")
    
    # Thống kê theo level
    level_stats = {}
    part_of_speech_stats = {}
    
    for doc in vocabularies.values():
        level = doc.get('level', 'Unknown')
        pos = doc.get('part_of_speech', 'Unknown')
        
        level_stats[level] = level_stats.get(level, 0) + 1
        part_of_speech_stats[pos] = part_of_speech_stats.get(pos, 0) + 1
    
    print(f"\n📚 Theo CEFR Level:")
    for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
        count = level_stats.get(level, 0)
        percentage = (count / total_count * 100) if total_count > 0 else 0
        print(f"  {level}: {count:,} từ ({percentage:.1f}%)")
    
    print(f"\n📝 Theo Part of Speech (Top 10):")
    sorted_pos = sorted(part_of_speech_stats.items(), key=lambda x: x[1], reverse=True)
    for pos, count in sorted_pos[:10]:
        percentage = (count / total_count * 100) if total_count > 0 else 0
        print(f"  {pos}: {count:,} từ ({percentage:.1f}%)")

def show_sample_data(firestore_data):
    """Hiển thị dữ liệu mẫu"""
    vocabularies = firestore_data["vocabularies"]
    
    print(f"\n📋 DỮ LIỆU MẪU:")
    sample_keys = list(vocabularies.keys())[:3]
    
    for key in sample_keys:
        doc = vocabularies[key]
        print(f"\n🔹 Document ID: {key}")
        print(f"   Word: {doc.get('word')}")
        print(f"   Level: {doc.get('level')}")
        print(f"   Meaning: {doc.get('vietnamese_meaning')}")
        print(f"   Examples: {len(doc.get('examples', []))} ví dụ")
        print(f"   Keywords: {len(doc.get('searchKeywords', []))} từ khóa")

def setup_firebase_config():
    """Setup Firebase configuration"""
    print("\n🔥 SETUP FIREBASE CONFIG")
    print("Bạn có 2 lựa chọn:")
    print("1. Upload Service Account Key file (.json)")
    print("2. Nhập Firebase config thủ công")

    choice = input("Chọn (1 hoặc 2): ").strip()

    if choice == "1":
        print("\n📁 Upload Service Account Key file:")
        print("(Tải từ Firebase Console > Project Settings > Service Accounts)")
        uploaded = files.upload()

        for filename in uploaded.keys():
            if filename.endswith('.json'):
                return filename

        raise Exception("❌ Không tìm thấy file JSON!")

    else:
        print("\n📝 Nhập Firebase config:")
        project_id = input("Project ID: ").strip()

        # Tạo config đơn giản
        config = {
            "type": "service_account",
            "project_id": project_id,
            "private_key_id": "dummy",
            "private_key": "-----BEGIN PRIVATE KEY-----\nDUMMY\n-----END PRIVATE KEY-----\n",
            "client_email": f"firebase-adminsdk@{project_id}.iam.gserviceaccount.com",
            "client_id": "dummy",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token"
        }

        with open('firebase_config.json', 'w') as f:
            json.dump(config, f)

        print("⚠️ Lưu ý: Config này chỉ để demo. Để import thực tế, cần Service Account Key thật.")
        return 'firebase_config.json'

def import_to_firebase(firestore_data, config_file):
    """Import dữ liệu trực tiếp vào Firebase"""
    try:
        print(f"\n🔥 ĐANG IMPORT VÀO FIREBASE...")

        # Khởi tạo Firebase
        if not firebase_admin._apps:
            cred = credentials.Certificate(config_file)
            firebase_admin.initialize_app(cred)

        db = firestore.client()

        # Import từng batch
        vocabularies = firestore_data["vocabularies"]
        batch_size = 500  # Giới hạn của Firestore
        total_docs = len(vocabularies)

        vocab_items = list(vocabularies.items())

        for i in range(0, total_docs, batch_size):
            batch = db.batch()
            batch_items = vocab_items[i:i + batch_size]

            for doc_id, doc_data in batch_items:
                doc_ref = db.collection('vocabularies').document(doc_id)
                batch.set(doc_ref, doc_data)

            batch.commit()

            imported = min(i + batch_size, total_docs)
            print(f"   ✅ Đã import {imported}/{total_docs} documents ({imported/total_docs*100:.1f}%)")

        print(f"\n🎉 IMPORT HOÀN TẤT!")
        print(f"📊 Đã import {total_docs} từ vựng vào Firestore")

        # Verify import
        sample_docs = db.collection('vocabularies').limit(3).get()
        print(f"\n✅ Verification - Tìm thấy {len(sample_docs)} documents:")
        for doc in sample_docs:
            data = doc.to_dict()
            print(f"   - {doc.id}: {data.get('word')} ({data.get('level')})")

        return True

    except Exception as e:
        print(f"❌ Lỗi import Firebase: {e}")
        print("💡 Có thể do:")
        print("   - Service Account Key không đúng")
        print("   - Project ID sai")
        print("   - Firestore chưa được enable")
        print("   - Quyền truy cập không đủ")
        return False

def download_result_file(filename):
    """Download file kết quả"""
    print(f"\n📥 Download file kết quả:")
    files.download(filename)

def main():
    """Hàm chính"""
    print("🔥 VOCABULARY TO FIRESTORE CONVERTER - PHIÊN BẢN HOÀN CHỈNH")
    print("=" * 60)

    try:
        # Bước 1: Upload file vocabulary
        json_file = upload_vocabulary_file()

        # Bước 2: Load dữ liệu
        vocabulary_data = load_vocabulary_data(json_file)

        if not vocabulary_data:
            print("❌ Không có dữ liệu để xử lý!")
            return

        # Bước 3: Convert sang Firestore format
        firestore_data = convert_to_firestore_format(vocabulary_data)

        # Bước 4: Tạo file backup
        output_file = create_firestore_import_file(firestore_data)

        # Bước 5: Hiển thị thống kê
        show_statistics(firestore_data)
        show_sample_data(firestore_data)

        # Bước 6: Lựa chọn import
        print(f"\n🎯 CHỌN PHƯƠNG THỨC IMPORT:")
        print("1. Import trực tiếp vào Firebase (khuyến nghị)")
        print("2. Chỉ download file để import sau")

        choice = input("Chọn (1 hoặc 2): ").strip()

        if choice == "1":
            # Setup Firebase và import
            try:
                config_file = setup_firebase_config()
                success = import_to_firebase(firestore_data, config_file)

                if success:
                    print(f"\n🎉 THÀNH CÔNG!")
                    print(f"✅ Đã import {len(firestore_data['vocabularies'])} từ vựng vào Firebase")
                    print(f"🔗 Kiểm tra tại: https://console.firebase.google.com")
                else:
                    print(f"\n⚠️ Import thất bại, download file để import thủ công")
                    download_result_file(output_file)

            except Exception as e:
                print(f"❌ Lỗi Firebase: {e}")
                print(f"📥 Download file để import thủ công:")
                download_result_file(output_file)
        else:
            # Chỉ download file
            print(f"\n✅ HOÀN THÀNH CONVERT!")
            print(f"📁 File output: {output_file}")
            print(f"💾 Kích thước: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")

            download_result_file(output_file)

            print(f"\n🚀 HƯỚNG DẪN IMPORT THỦ CÔNG:")
            print(f"1. Cài Node.js: https://nodejs.org")
            print(f"2. Cài Firebase CLI: npm install -g firebase-tools")
            print(f"3. Đăng nhập: firebase login")
            print(f"4. Import: firebase firestore:import {output_file}")

        print(f"\n🎊 CẢM ƠN BẠN ĐÃ SỬ DỤNG!")

    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

# Chạy script
if __name__ == "__main__":
    main()
