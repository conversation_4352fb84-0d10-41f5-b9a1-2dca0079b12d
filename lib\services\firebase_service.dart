import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import '../screens/vocabulary_screen/model/vocabulary_item.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  late FirebaseFirestore _firestore;
  bool _isInitialized = false;

  // Initialize Firebase
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await Firebase.initializeApp();
      _firestore = FirebaseFirestore.instance;
      _isInitialized = true;
      print('✅ Firebase initialized successfully');
    } catch (e) {
      print('❌ Firebase initialization error: $e');
      rethrow;
    }
  }

  // Check if Firebase is initialized
  bool get isInitialized => _isInitialized;

  // Collections references
  CollectionReference get vocabulariesCollection =>
      _firestore.collection('vocabularies');

  CollectionReference get usersCollection => _firestore.collection('users');

  // ===== VOCABULARY METHODS =====

  // Get all vocabularies
  Future<List<VocabularyWord>> getAllVocabularies() async {
    try {
      print('🔍 Fetching vocabularies from Firestore...');
      final querySnapshot = await vocabulariesCollection.get();
      print(
          '🔍 Found ${querySnapshot.docs.length} documents in vocabularies collection');

      if (querySnapshot.docs.isEmpty) {
        print('⚠️ No documents found in vocabularies collection');
        return [];
      }

      // Debug first document
      if (querySnapshot.docs.isNotEmpty) {
        final firstDoc = querySnapshot.docs.first;
        print('🔍 First document data: ${firstDoc.data()}');
      }

      return querySnapshot.docs
          .map((doc) => VocabularyWord.fromJson(
              {'id': doc.id, ...doc.data() as Map<String, dynamic>}))
          .toList();
    } catch (e) {
      print('❌ Error getting vocabularies: $e');
      return [];
    }
  }

  // Get vocabularies by level
  Future<List<VocabularyWord>> getVocabulariesByLevel(String level) async {
    try {
      print('🔍 Querying vocabularies for level: $level');
      final querySnapshot =
          await vocabulariesCollection.where('level', isEqualTo: level).get();

      print('🔍 Found ${querySnapshot.docs.length} documents for level $level');

      if (querySnapshot.docs.isNotEmpty) {
        final firstDoc = querySnapshot.docs.first;
        print('🔍 Sample document for $level: ${firstDoc.data()}');
      }

      return querySnapshot.docs
          .map((doc) => VocabularyWord.fromJson(
              {'id': doc.id, ...doc.data() as Map<String, dynamic>}))
          .toList();
    } catch (e) {
      print('❌ Error getting vocabularies by level: $e');
      return [];
    }
  }

  // Search vocabularies
  Future<List<VocabularyWord>> searchVocabularies(String query) async {
    try {
      // Firestore doesn't support full-text search, so we'll use array-contains
      // For better search, consider using Algolia or ElasticSearch
      final querySnapshot = await vocabulariesCollection
          .where('searchKeywords', arrayContains: query.toLowerCase())
          .get();

      return querySnapshot.docs
          .map((doc) => VocabularyWord.fromJson(
              {'id': doc.id, ...doc.data() as Map<String, dynamic>}))
          .toList();
    } catch (e) {
      print('❌ Error searching vocabularies: $e');
      return [];
    }
  }

  // Add vocabulary
  Future<String?> addVocabulary(VocabularyWord vocabulary) async {
    try {
      final docData = vocabulary.toJson();

      // Add search keywords for better search functionality
      docData['searchKeywords'] = _generateSearchKeywords(vocabulary);
      docData['createdAt'] = FieldValue.serverTimestamp();
      docData['updatedAt'] = FieldValue.serverTimestamp();

      final docRef = await vocabulariesCollection.add(docData);
      print('✅ Vocabulary added with ID: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      print('❌ Error adding vocabulary: $e');
      return null;
    }
  }

  // Update vocabulary
  Future<bool> updateVocabulary(String id, VocabularyWord vocabulary) async {
    try {
      final docData = vocabulary.toJson();
      docData['searchKeywords'] = _generateSearchKeywords(vocabulary);
      docData['updatedAt'] = FieldValue.serverTimestamp();

      await vocabulariesCollection.doc(id).update(docData);
      print('✅ Vocabulary updated: $id');
      return true;
    } catch (e) {
      print('❌ Error updating vocabulary: $e');
      return false;
    }
  }

  // Delete vocabulary
  Future<bool> deleteVocabulary(String id) async {
    try {
      await vocabulariesCollection.doc(id).delete();
      print('✅ Vocabulary deleted: $id');
      return true;
    } catch (e) {
      print('❌ Error deleting vocabulary: $e');
      return false;
    }
  }

  // Batch upload vocabularies (for initial data import)
  Future<bool> batchUploadVocabularies(
      List<VocabularyWord> vocabularies) async {
    try {
      final batch = _firestore.batch();

      for (final vocabulary in vocabularies) {
        final docRef = vocabulariesCollection.doc();
        final docData = vocabulary.toJson();
        docData['searchKeywords'] = _generateSearchKeywords(vocabulary);
        docData['createdAt'] = FieldValue.serverTimestamp();
        docData['updatedAt'] = FieldValue.serverTimestamp();

        batch.set(docRef, docData);
      }

      await batch.commit();
      print('✅ Batch uploaded ${vocabularies.length} vocabularies');
      return true;
    } catch (e) {
      print('❌ Error batch uploading vocabularies: $e');
      return false;
    }
  }

  // Get vocabulary statistics
  Future<Map<String, int>> getVocabularyStatistics() async {
    try {
      final stats = <String, int>{};

      // Get total count
      final totalSnapshot = await vocabulariesCollection.count().get();
      stats['total'] = totalSnapshot.count ?? 0;

      // Get count by level
      final levels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
      for (final level in levels) {
        final levelSnapshot = await vocabulariesCollection
            .where('level', isEqualTo: level)
            .count()
            .get();
        stats[level] = levelSnapshot.count ?? 0;
      }

      return stats;
    } catch (e) {
      print('❌ Error getting vocabulary statistics: $e');
      return {};
    }
  }

  // Stream vocabularies (real-time updates)
  Stream<List<VocabularyWord>> streamVocabularies({String? level}) {
    Query query = vocabulariesCollection.orderBy('word');

    if (level != null && level != 'All') {
      query = query.where('level', isEqualTo: level);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => VocabularyWord.fromJson(
              {'id': doc.id, ...doc.data() as Map<String, dynamic>}))
          .toList();
    });
  }

  // ===== HELPER METHODS =====

  // Generate search keywords for better search functionality
  List<String> _generateSearchKeywords(VocabularyWord vocabulary) {
    final keywords = <String>{};

    // Add word variations
    keywords.add(vocabulary.word.toLowerCase());
    keywords.add(vocabulary.vietnameseMeaning.toLowerCase());

    // Add word parts (for partial search)
    for (int i = 1; i <= vocabulary.word.length; i++) {
      keywords.add(vocabulary.word.substring(0, i).toLowerCase());
    }

    // Add meaning parts
    final meaningWords = vocabulary.vietnameseMeaning.toLowerCase().split(' ');
    keywords.addAll(meaningWords);

    // Add examples
    for (final example in vocabulary.examples) {
      final englishWords = example.english.toLowerCase().split(' ');
      final vietnameseWords = example.vietnamese.toLowerCase().split(' ');
      keywords.addAll(englishWords);
      keywords.addAll(vietnameseWords);
    }

    return keywords.toList();
  }

  // Import data from JSON (one-time migration)
  Future<bool> importFromJson(List<Map<String, dynamic>> jsonData) async {
    try {
      final vocabularies =
          jsonData.map((json) => VocabularyWord.fromJson(json)).toList();

      return await batchUploadVocabularies(vocabularies);
    } catch (e) {
      print('❌ Error importing from JSON: $e');
      return false;
    }
  }

  // Clear all vocabularies (use with caution!)
  Future<bool> clearAllVocabularies() async {
    try {
      final snapshot = await vocabulariesCollection.get();
      final batch = _firestore.batch();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      print('✅ All vocabularies cleared');
      return true;
    } catch (e) {
      print('❌ Error clearing vocabularies: $e');
      return false;
    }
  }
}
