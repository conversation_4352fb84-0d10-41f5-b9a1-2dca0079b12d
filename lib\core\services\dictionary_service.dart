// import 'package:sqflite/sqflite.dart';
// import '../models/dictionary_entry.dart';
// import 'database_helper.dart';
// import 'level_classifier_service.dart';

// /// Service để tương tác với từ điển
// class DictionaryService {
//   final DatabaseHelper _dbHelper = DatabaseHelper();

//   /// Tìm kiếm từ trong từ điển
//   Future<List<DictionaryEntry>> searchWords(String query) async {
//     if (query.trim().isEmpty) {
//       return [];
//     }

//     try {
//       return await _dbHelper.searchWords(query.trim());
//     } catch (e) {
//       print('Lỗi khi tìm kiếm từ: $e');
//       return [];
//     }
//   }

//   /// Lấy từ theo ID
//   Future<DictionaryEntry?> getWordById(int id) async {
//     try {
//       return await _dbHelper.getDictionaryEntryById(id);
//     } catch (e) {
//       print('Lỗi khi lấy từ theo ID: $e');
//       return null;
//     }
//   }

//   /// L<PERSON>y tất cả từ (có phân trang)
//   Future<List<DictionaryEntry>> getAllWords({
//     int limit = 50,
//     int offset = 0,
//   }) async {
//     try {
//       final db = await _dbHelper.database;
//       final List<Map<String, dynamic>> maps = await db.query(
//         'dictionary_entries',
//         orderBy: 'word ASC',
//         limit: limit,
//         offset: offset,
//       );

//       return List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });
//     } catch (e) {
//       print('Lỗi khi lấy danh sách từ: $e');
//       return [];
//     }
//   }

//   /// Lấy từ ngẫu nhiên
//   Future<List<DictionaryEntry>> getRandomWords(int count) async {
//     try {
//       final db = await _dbHelper.database;
//       final List<Map<String, dynamic>> maps = await db.rawQuery(
//         'SELECT * FROM dictionary_entries ORDER BY RANDOM() LIMIT ?',
//         [count],
//       );

//       return List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });
//     } catch (e) {
//       print('Lỗi khi lấy từ ngẫu nhiên: $e');
//       return [];
//     }
//   }

//   /// Lấy từ theo loại từ
//   Future<List<DictionaryEntry>> getWordsByType(String wordType) async {
//     try {
//       final db = await _dbHelper.database;
//       final List<Map<String, dynamic>> maps = await db.query(
//         'dictionary_entries',
//         where: 'word_type LIKE ?',
//         whereArgs: ['%$wordType%'],
//         orderBy: 'word ASC',
//       );

//       return List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });
//     } catch (e) {
//       print('Lỗi khi lấy từ theo loại: $e');
//       return [];
//     }
//   }

//   /// Tìm kiếm nâng cao
//   Future<List<DictionaryEntry>> advancedSearch({
//     String? word,
//     String? wordType,
//     String? definition,
//     bool exactMatch = false,
//   }) async {
//     try {
//       final db = await _dbHelper.database;
//       final List<String> whereConditions = [];
//       final List<String> whereArgs = [];

//       if (word != null && word.isNotEmpty) {
//         if (exactMatch) {
//           whereConditions.add('word = ?');
//           whereArgs.add(word);
//         } else {
//           whereConditions.add('word LIKE ?');
//           whereArgs.add('%$word%');
//         }
//       }

//       if (wordType != null && wordType.isNotEmpty) {
//         whereConditions.add('word_type LIKE ?');
//         whereArgs.add('%$wordType%');
//       }

//       if (definition != null && definition.isNotEmpty) {
//         whereConditions.add('definition LIKE ?');
//         whereArgs.add('%$definition%');
//       }

//       if (whereConditions.isEmpty) {
//         return [];
//       }

//       final List<Map<String, dynamic>> maps = await db.query(
//         'dictionary_entries',
//         where: whereConditions.join(' AND '),
//         whereArgs: whereArgs,
//         orderBy: 'word ASC',
//       );

//       return List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });
//     } catch (e) {
//       print('Lỗi khi tìm kiếm nâng cao: $e');
//       return [];
//     }
//   }

//   /// Lấy gợi ý từ (autocomplete)
//   Future<List<String>> getSuggestions(String query, {int limit = 10}) async {
//     if (query.trim().isEmpty) {
//       return [];
//     }

//     try {
//       final db = await _dbHelper.database;
//       final List<Map<String, dynamic>> maps = await db.query(
//         'dictionary_entries',
//         columns: ['word'],
//         where: 'word LIKE ?',
//         whereArgs: ['$query%'],
//         orderBy: 'word ASC',
//         limit: limit,
//       );

//       return maps.map((map) => map['word'] as String).toList();
//     } catch (e) {
//       print('Lỗi khi lấy gợi ý: $e');
//       return [];
//     }
//   }

//   /// Lấy thống kê từ điển
//   Future<DictionaryStatistics> getStatistics() async {
//     try {
//       final db = await _dbHelper.database;

//       // Tổng số từ
//       final totalResult =
//           await db.rawQuery('SELECT COUNT(*) as count FROM dictionary_entries');
//       final totalWords = Sqflite.firstIntValue(totalResult) ?? 0;

//       // Thống kê theo loại từ
//       final typeResult = await db.rawQuery('''
//         SELECT word_type, COUNT(*) as count 
//         FROM dictionary_entries 
//         WHERE word_type IS NOT NULL AND word_type != ''
//         GROUP BY word_type 
//         ORDER BY count DESC
//       ''');

//       final Map<String, int> wordTypeStats = {};
//       for (final row in typeResult) {
//         wordTypeStats[row['word_type'] as String] = row['count'] as int;
//       }

//       // Từ có nhiều ví dụ nhất
//       final exampleResult = await db.rawQuery('''
//         SELECT word, LENGTH(examples) - LENGTH(REPLACE(examples, ',', '')) + 1 as example_count
//         FROM dictionary_entries 
//         WHERE examples IS NOT NULL AND examples != '' AND examples != '[]'
//         ORDER BY example_count DESC 
//         LIMIT 10
//       ''');

//       final List<Map<String, dynamic>> wordsWithMostExamples = exampleResult;

//       return DictionaryStatistics(
//         totalWords: totalWords,
//         wordTypeStatistics: wordTypeStats,
//         wordsWithMostExamples: wordsWithMostExamples,
//       );
//     } catch (e) {
//       print('Lỗi khi lấy thống kê: $e');
//       return DictionaryStatistics(
//         totalWords: 0,
//         wordTypeStatistics: {},
//         wordsWithMostExamples: [],
//       );
//     }
//   }

//   /// Thêm từ vào yêu thích
//   Future<bool> addToFavorites(int wordId) async {
//     try {
//       final db = await _dbHelper.database;
//       await db.insert('favorite_words', {
//         'word_id': wordId,
//         'created_at': DateTime.now().toIso8601String(),
//       });
//       return true;
//     } catch (e) {
//       print('Lỗi khi thêm vào yêu thích: $e');
//       return false;
//     }
//   }

//   /// Xóa từ khỏi yêu thích
//   Future<bool> removeFromFavorites(int wordId) async {
//     try {
//       final db = await _dbHelper.database;
//       await db.delete(
//         'favorite_words',
//         where: 'word_id = ?',
//         whereArgs: [wordId],
//       );
//       return true;
//     } catch (e) {
//       print('Lỗi khi xóa khỏi yêu thích: $e');
//       return false;
//     }
//   }

//   /// Lấy danh sách từ yêu thích
//   Future<List<DictionaryEntry>> getFavoriteWords() async {
//     try {
//       final db = await _dbHelper.database;
//       final List<Map<String, dynamic>> maps = await db.rawQuery('''
//         SELECT de.* FROM dictionary_entries de
//         INNER JOIN favorite_words fw ON de.id = fw.word_id
//         ORDER BY fw.created_at DESC
//       ''');

//       return List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });
//     } catch (e) {
//       print('Lỗi khi lấy từ yêu thích: $e');
//       return [];
//     }
//   }

//   /// Kiểm tra từ có trong yêu thích không
//   Future<bool> isFavorite(int wordId) async {
//     try {
//       final db = await _dbHelper.database;
//       final result = await db.query(
//         'favorite_words',
//         where: 'word_id = ?',
//         whereArgs: [wordId],
//       );
//       return result.isNotEmpty;
//     } catch (e) {
//       print('Lỗi khi kiểm tra yêu thích: $e');
//       return false;
//     }
//   }

//   /// Lấy lịch sử tìm kiếm
//   Future<List<String>> getSearchHistory({int limit = 20}) async {
//     try {
//       final db = await _dbHelper.database;
//       final List<Map<String, dynamic>> maps = await db.query(
//         'search_history',
//         orderBy: 'last_searched DESC',
//         limit: limit,
//       );

//       return maps.map((map) => map['search_term'] as String).toList();
//     } catch (e) {
//       print('Lỗi khi lấy lịch sử tìm kiếm: $e');
//       return [];
//     }
//   }

//   /// Xóa lịch sử tìm kiếm
//   Future<bool> clearSearchHistory() async {
//     try {
//       final db = await _dbHelper.database;
//       await db.delete('search_history');
//       return true;
//     } catch (e) {
//       print('Lỗi khi xóa lịch sử: $e');
//       return false;
//     }
//   }

//   // ===== LEVEL-RELATED METHODS =====

//   /// Lấy từ theo level CEFR
//   Future<List<DictionaryEntry>> getWordsByLevel(
//     CEFRLevel level, {
//     int limit = 50,
//     int offset = 0,
//   }) async {
//     try {
//       final db = await _dbHelper.database;
//       final List<Map<String, dynamic>> maps = await db.query(
//         'dictionary_entries',
//         where: 'level = ?',
//         whereArgs: [level.code],
//         orderBy: 'word ASC',
//         limit: limit,
//         offset: offset,
//       );

//       return List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });
//     } catch (e) {
//       print('Lỗi khi lấy từ theo level: $e');
//       return [];
//     }
//   }

//   /// Lấy từ theo nhiều level
//   Future<List<DictionaryEntry>> getWordsByLevels(
//     List<CEFRLevel> levels, {
//     int limit = 50,
//     int offset = 0,
//   }) async {
//     try {
//       final db = await _dbHelper.database;
//       final levelCodes = levels.map((l) => l.code).toList();
//       final placeholders = levelCodes.map((_) => '?').join(',');

//       final List<Map<String, dynamic>> maps = await db.query(
//         'dictionary_entries',
//         where: 'level IN ($placeholders)',
//         whereArgs: levelCodes,
//         orderBy: 'word ASC',
//         limit: limit,
//         offset: offset,
//       );

//       return List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });
//     } catch (e) {
//       print('Lỗi khi lấy từ theo nhiều level: $e');
//       return [];
//     }
//   }

//   /// Tìm kiếm từ theo level
//   Future<List<DictionaryEntry>> searchWordsByLevel(
//     String query,
//     CEFRLevel level,
//   ) async {
//     try {
//       final db = await _dbHelper.database;
//       final List<Map<String, dynamic>> maps = await db.query(
//         'dictionary_entries',
//         where:
//             'level = ? AND (word LIKE ? OR definition LIKE ? OR pronunciation LIKE ?)',
//         whereArgs: [level.code, '%$query%', '%$query%', '%$query%'],
//         orderBy: 'word ASC',
//       );

//       return List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });
//     } catch (e) {
//       print('Lỗi khi tìm kiếm từ theo level: $e');
//       return [];
//     }
//   }

//   /// Lấy từ ngẫu nhiên theo level
//   Future<List<DictionaryEntry>> getRandomWordsByLevel(
//     CEFRLevel level,
//     int count,
//   ) async {
//     try {
//       final db = await _dbHelper.database;
//       final List<Map<String, dynamic>> maps = await db.rawQuery(
//         'SELECT * FROM dictionary_entries WHERE level = ? ORDER BY RANDOM() LIMIT ?',
//         [level.code, count],
//       );

//       return List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });
//     } catch (e) {
//       print('Lỗi khi lấy từ ngẫu nhiên theo level: $e');
//       return [];
//     }
//   }

//   /// Đếm số từ theo level
//   Future<int> countWordsByLevel(CEFRLevel level) async {
//     try {
//       final db = await _dbHelper.database;
//       final result = await db.rawQuery(
//         'SELECT COUNT(*) as count FROM dictionary_entries WHERE level = ?',
//         [level.code],
//       );
//       return Sqflite.firstIntValue(result) ?? 0;
//     } catch (e) {
//       print('Lỗi khi đếm từ theo level: $e');
//       return 0;
//     }
//   }

//   /// Lấy thống kê phân bố level
//   Future<Map<CEFRLevel, int>> getLevelDistribution() async {
//     try {
//       final db = await _dbHelper.database;
//       final result = await db.rawQuery('''
//         SELECT level, COUNT(*) as count
//         FROM dictionary_entries
//         WHERE level IS NOT NULL
//         GROUP BY level
//       ''');

//       final Map<CEFRLevel, int> distribution = {};

//       // Khởi tạo tất cả level với 0
//       for (final level in CEFRLevel.values) {
//         distribution[level] = 0;
//       }

//       // Cập nhật với dữ liệu thực tế
//       for (final row in result) {
//         final levelCode = row['level'] as String?;
//         final count = row['count'] as int;
//         final level = CEFRLevel.fromCode(levelCode);
//         if (level != null) {
//           distribution[level] = count;
//         }
//       }

//       return distribution;
//     } catch (e) {
//       print('Lỗi khi lấy thống kê level: $e');
//       return {};
//     }
//   }

//   /// Cập nhật level cho từ
//   Future<bool> updateWordLevel(int wordId, CEFRLevel level) async {
//     try {
//       final db = await _dbHelper.database;
//       final result = await db.update(
//         'dictionary_entries',
//         {'level': level.code, 'updated_at': DateTime.now().toIso8601String()},
//         where: 'id = ?',
//         whereArgs: [wordId],
//       );
//       return result > 0;
//     } catch (e) {
//       print('Lỗi khi cập nhật level: $e');
//       return false;
//     }
//   }

//   /// Phân loại level tự động cho từ chưa có level
//   Future<int> autoClassifyLevels({int batchSize = 100}) async {
//     try {
//       final classifier = LevelClassifierService();
//       final db = await _dbHelper.database;

//       // Lấy từ chưa có level
//       final List<Map<String, dynamic>> maps = await db.query(
//         'dictionary_entries',
//         where: 'level IS NULL',
//         limit: batchSize,
//       );

//       if (maps.isEmpty) return 0;

//       final entries = List.generate(maps.length, (i) {
//         return DictionaryEntry.fromMap(maps[i]);
//       });

//       int updatedCount = 0;
//       final batch = db.batch();

//       for (final entry in entries) {
//         if (entry.id != null) {
//           final level = classifier.classifyWord(entry);
//           batch.update(
//             'dictionary_entries',
//             {
//               'level': level.code,
//               'updated_at': DateTime.now().toIso8601String(),
//             },
//             where: 'id = ?',
//             whereArgs: [entry.id],
//           );
//           updatedCount++;
//         }
//       }

//       await batch.commit(noResult: true);
//       return updatedCount;
//     } catch (e) {
//       print('Lỗi khi phân loại level tự động: $e');
//       return 0;
//     }
//   }
// }

// /// Class cho thống kê từ điển
// class DictionaryStatistics {
//   final int totalWords;
//   final Map<String, int> wordTypeStatistics;
//   final List<Map<String, dynamic>> wordsWithMostExamples;

//   DictionaryStatistics({
//     required this.totalWords,
//     required this.wordTypeStatistics,
//     required this.wordsWithMostExamples,
//   });
// }
