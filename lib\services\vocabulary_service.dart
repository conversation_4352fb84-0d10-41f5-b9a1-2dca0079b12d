import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart';
import '../screens/vocabulary_screen/model/vocabulary_item.dart';
import 'firebase_service.dart';

class VocabularyService {
  static final VocabularyService _instance = VocabularyService._internal();
  factory VocabularyService() => _instance;
  VocabularyService._internal();

  List<VocabularyWord> _vocabularyWords = [];
  bool _isLoaded = false;
  final FirebaseService _firebaseService = FirebaseService();

  // Getter để kiểm tra xem dữ liệu đã được load chưa
  bool get isLoaded => _isLoaded;

  // Getter để lấy tất cả từ vựng
  List<VocabularyWord> get allWords => _vocabularyWords;

  // Load dữ liệu từ file JSON trong assets
  Future<void> loadVocabularyFromAssets(String assetPath) async {
    try {
      final String jsonString = await rootBundle.loadString(assetPath);
      final List<dynamic> jsonList = json.decode(jsonString);

      _vocabularyWords = jsonList
          .map((json) => VocabularyWord.fromJson(json as Map<String, dynamic>))
          .toList();

      _isLoaded = true;
      print('✅ Đã load ${_vocabularyWords.length} từ vựng từ $assetPath');
    } catch (e) {
      print('❌ Lỗi khi load từ vựng: $e');
      _isLoaded = false;
    }
  }

  // Load dữ liệu từ JSON string (cho trường hợp paste trực tiếp)
  void loadVocabularyFromJson(String jsonString) {
    try {
      final List<dynamic> jsonList = json.decode(jsonString);

      _vocabularyWords = jsonList
          .map((json) => VocabularyWord.fromJson(json as Map<String, dynamic>))
          .toList();

      _isLoaded = true;
      print('✅ Đã load ${_vocabularyWords.length} từ vựng từ JSON string');
    } catch (e) {
      print('❌ Lỗi khi parse JSON: $e');
      _isLoaded = false;
    }
  }

  // Lấy từ vựng theo level CEFR
  List<VocabularyWord> getWordsByLevel(String level) {
    return _vocabularyWords.where((word) => word.level == level).toList();
  }

  // Lấy từ vựng ngẫu nhiên
  List<VocabularyWord> getRandomWords(int count) {
    if (_vocabularyWords.isEmpty) return [];

    final shuffled = List<VocabularyWord>.from(_vocabularyWords);
    shuffled.shuffle();
    return shuffled.take(count).toList();
  }

  // Tìm kiếm từ vựng
  List<VocabularyWord> searchWords(String query) {
    if (query.isEmpty) return _vocabularyWords;

    final lowerQuery = query.toLowerCase();
    return _vocabularyWords.where((word) {
      return word.word.toLowerCase().contains(lowerQuery) ||
          word.vietnameseMeaning.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  // Lấy từ vựng theo part of speech
  List<VocabularyWord> getWordsByPartOfSpeech(String partOfSpeech) {
    return _vocabularyWords
        .where((word) => word.partOfSpeech == partOfSpeech)
        .toList();
  }

  // Chuyển đổi sang VocabularyItem để tương thích với UI hiện tại
  List<VocabularyItem> getVocabularyItems({
    String? level,
    int? limit,
    String? search,
  }) {
    List<VocabularyWord> filteredWords = _vocabularyWords;

    // Lọc theo level
    if (level != null && level.isNotEmpty) {
      filteredWords =
          filteredWords.where((word) => word.level == level).toList();
    }

    // Lọc theo từ khóa tìm kiếm
    if (search != null && search.isNotEmpty) {
      final lowerSearch = search.toLowerCase();
      filteredWords = filteredWords.where((word) {
        return word.word.toLowerCase().contains(lowerSearch) ||
            word.vietnameseMeaning.toLowerCase().contains(lowerSearch);
      }).toList();
    }

    // Giới hạn số lượng
    if (limit != null && limit > 0) {
      filteredWords = filteredWords.take(limit).toList();
    }

    // Chuyển đổi sang VocabularyItem và cải thiện choices
    return filteredWords.map((word) => _enhanceVocabularyItem(word)).toList();
  }

  // Cải thiện VocabularyItem với choices thông minh hơn
  VocabularyItem _enhanceVocabularyItem(VocabularyWord word) {
    // Debug log để kiểm tra conversion
    print(
        '🔍 Converting word: ${word.word} (${word.level}) - ${word.vietnameseMeaning}');

    return VocabularyItem(
      word: word.word,
      pronunciation: _generatePronunciation(word.word), // Tạm thời generate
      meaning: word.vietnameseMeaning,
      example: word.examples.isNotEmpty
          ? '${word.examples.first.english}\n(${word.examples.first.vietnamese})'
          : 'No example available',
      imageUrl: _generateImageUrl(word.word), // Tạm thời generate
      choices: _generateSmartChoices(word),
      exercises: [],
    );
  }

  // Tạo phiên âm tạm thời (sau này có thể integrate với API)
  String _generatePronunciation(String word) {
    // Tạm thời trả về format cơ bản
    return '/${word.toLowerCase()}/';
  }

  // Tạo URL hình ảnh tạm thời
  String _generateImageUrl(String word) {
    // Tạm thời trả về placeholder
    return 'assets/images/vocabulary_placeholder.png';
  }

  // Tạo choices thông minh cho multiple choice
  List<String> _generateSmartChoices(VocabularyWord correctWord) {
    final choices = <String>[correctWord.vietnameseMeaning];

    // Lấy các từ khác cùng level để tạo choices sai
    final sameLevel = _vocabularyWords
        .where(
            (w) => w.level == correctWord.level && w.word != correctWord.word)
        .toList();

    if (sameLevel.length >= 3) {
      sameLevel.shuffle();
      choices.addAll(sameLevel.take(3).map((w) => w.vietnameseMeaning));
    } else {
      // Nếu không đủ từ cùng level, lấy từ level khác
      final otherWords =
          _vocabularyWords.where((w) => w.word != correctWord.word).toList();
      otherWords.shuffle();

      final needed = 4 - choices.length;
      choices.addAll(otherWords.take(needed).map((w) => w.vietnameseMeaning));
    }

    // Trộn thứ tự choices
    choices.shuffle();
    return choices;
  }

  // Lấy thống kê từ vựng
  Map<String, int> getStatistics() {
    final stats = <String, int>{};

    // Đếm theo level
    for (final word in _vocabularyWords) {
      stats[word.level] = (stats[word.level] ?? 0) + 1;
    }

    // Tổng số từ
    stats['total'] = _vocabularyWords.length;

    return stats;
  }

  // ===== FIREBASE METHODS =====

  // Initialize Firebase
  Future<void> initializeFirebase() async {
    await _firebaseService.initialize();
  }

  // Load vocabularies from Firebase
  Future<void> loadVocabulariesFromFirebase({String? level}) async {
    try {
      if (!_firebaseService.isInitialized) {
        await _firebaseService.initialize();
      }

      // Luôn load tất cả dữ liệu trước, sau đó filter trong memory
      // Điều này giúp app có đầy đủ dữ liệu cho tất cả levels
      List<VocabularyWord> words = await _firebaseService.getAllVocabularies();

      print('🔍 Loaded ${words.length} total vocabularies from Firebase');

      // Debug: Count by level
      final levelCounts = <String, int>{};
      for (final word in words) {
        levelCounts[word.level] = (levelCounts[word.level] ?? 0) + 1;
      }
      print('🔍 Level distribution: $levelCounts');

      _vocabularyWords = words;
      _isLoaded = true;
      print('✅ Đã load ${_vocabularyWords.length} từ vựng từ Firebase');
    } catch (e) {
      print('❌ Lỗi khi load từ vựng từ Firebase: $e');
      _isLoaded = false;
    }
  }

  // Search vocabularies in Firebase
  Future<List<VocabularyWord>> searchVocabulariesInFirebase(
      String query) async {
    try {
      if (!_firebaseService.isInitialized) {
        await _firebaseService.initialize();
      }
      return await _firebaseService.searchVocabularies(query);
    } catch (e) {
      print('❌ Lỗi khi tìm kiếm từ vựng: $e');
      return [];
    }
  }

  // Add vocabulary to Firebase
  Future<String?> addVocabularyToFirebase(VocabularyWord vocabulary) async {
    try {
      if (!_firebaseService.isInitialized) {
        await _firebaseService.initialize();
      }
      final id = await _firebaseService.addVocabulary(vocabulary);
      if (id != null) {
        _vocabularyWords.add(vocabulary);
      }
      return id;
    } catch (e) {
      print('❌ Lỗi khi thêm từ vựng: $e');
      return null;
    }
  }

  // Get Firebase statistics
  Future<Map<String, int>> getFirebaseStatistics() async {
    try {
      if (!_firebaseService.isInitialized) {
        await _firebaseService.initialize();
      }
      return await _firebaseService.getVocabularyStatistics();
    } catch (e) {
      print('❌ Lỗi khi lấy thống kê: $e');
      return {};
    }
  }

  // Stream vocabularies (real-time updates)
  Stream<List<VocabularyWord>> streamVocabularies({String? level}) {
    if (!_firebaseService.isInitialized) {
      return Stream.empty();
    }
    return _firebaseService.streamVocabularies(level: level);
  }

  // Set cached words (for cache integration)
  void setCachedWords(List<VocabularyWord> words) {
    _vocabularyWords = words;
    _isLoaded = true;
    print('📱 Set ${words.length} cached words to vocabulary service');
  }

  // Reset dữ liệu
  void reset() {
    _vocabularyWords.clear();
    _isLoaded = false;
  }

  // Thêm từ vựng mới (cho tương lai)
  void addWord(VocabularyWord word) {
    _vocabularyWords.add(word);
  }

  // Xóa từ vựng (cho tương lai)
  void removeWord(String word) {
    _vocabularyWords.removeWhere((w) => w.word == word);
  }
}
