import 'package:flutter/material.dart';

import '../core/app_export.dart';
import './custom_image_view.dart';

/**
 * CustomIconButton - A reusable icon button component with customizable styling
 * 
 * Features:
 * - Configurable icon image path
 * - Customizable colors, sizes, and border radius
 * - Tap callback support with visual feedback
 * - Responsive design using SizeUtils
 * - Default styling matching the design system
 * 
 * @param iconPath - Path to the icon image (SVG, PNG, etc.)
 * @param onTap - Callback function when button is tapped
 * @param width - Button width (defaults to 48.h)
 * @param height - Button height (defaults to 48.h)
 * @param backgroundColor - Background color (defaults to #f2f2f4)
 * @param borderRadius - Border radius (defaults to 8.h)
 * @param iconSize - Icon size (defaults to 24.h)
 * @param iconColor - Icon color for tinting
 */
class CustomIconButton extends StatelessWidget {
  const CustomIconButton({
    Key? key,
    required this.iconPath,
    this.onTap,
    this.width,
    this.height,
    this.backgroundColor,
    this.borderRadius,
    this.iconSize,
    this.iconColor,
  }) : super(key: key);

  /// Path to the icon image
  final String iconPath;

  /// Callback function when button is tapped
  final VoidCallback? onTap;

  /// Button width
  final double? width;

  /// Button height
  final double? height;

  /// Background color of the button
  final Color? backgroundColor;

  /// Border radius of the button
  final double? borderRadius;

  /// Size of the icon
  final double? iconSize;

  /// Color tint for the icon
  final Color? iconColor;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(borderRadius ?? 8.h),
      child: Container(
        width: width ?? 48.h,
        height: height ?? 48.h,
        decoration: BoxDecoration(
          color: backgroundColor ?? appTheme.colorFFF2F2,
          borderRadius: BorderRadius.circular(borderRadius ?? 8.h),
        ),
        child: Center(
          child: CustomImageView(
            imagePath: iconPath,
            width: iconSize ?? 24.h,
            height: iconSize ?? 24.h,
            color: iconColor,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}
