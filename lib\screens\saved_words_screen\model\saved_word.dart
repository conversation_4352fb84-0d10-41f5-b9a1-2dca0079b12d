import 'package:flutter/material.dart';

class SavedWord {
  final String word;
  final String pronunciation;
  final String meaning;
  final String example;
  final bool isFavorite;
  final DateTime savedDate;
  final int reviewCount;

  SavedWord({
    required this.word,
    required this.pronunciation,
    required this.meaning,
    required this.example,
    this.isFavorite = false,
    required this.savedDate,
    this.reviewCount = 0,
  });
}

class BookSection {
  final String title;
  final String subtitle;
  final Color color;
  final IconData icon;
  final int itemCount;
  final double progress;

  BookSection({
    required this.title,
    required this.subtitle,
    required this.color,
    required this.icon,
    required this.itemCount,
    required this.progress,
  });
}
