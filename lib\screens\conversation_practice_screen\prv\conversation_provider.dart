import 'package:flutter/material.dart';
import '../../ai_chat_screen/model/chat_message.dart';

class ConversationProvider with ChangeNotifier {
  PracticeUser? _currentPartner;
  bool _isMuted = false;
  bool _isVolumeOn = true;
  bool _isCallActive = false;
  Duration _callDuration = Duration.zero;
  String? _selectedConversationStarter;

  final List<ConversationStarter> _conversationStarters = [
    ConversationStarter(
        id: '1', question: "What's your favorite book and why?"),
    ConversationStarter(
        id: '2', question: "Tell me about a memorable trip you've taken."),
    ConversationStarter(
        id: '3', question: "What's a skill you'd like to learn?"),
    ConversationStarter(
        id: '4', question: "What's your favorite type of music?"),
    ConversationStarter(id: '5', question: "Describe your ideal weekend."),
    ConversationStarter(
        id: '6', question: "What's a goal you're currently working towards?"),
  ];

  // Getters
  PracticeUser? get currentPartner => _currentPartner;
  bool get isMuted => _isMuted;
  bool get isVolumeOn => _isVolumeOn;
  bool get isCallActive => _isCallActive;
  Duration get callDuration => _callDuration;
  String? get selectedConversationStarter => _selectedConversationStarter;
  List<ConversationStarter> get conversationStarters => _conversationStarters;

  void startConversation(PracticeUser partner) {
    _currentPartner = partner;
    _isCallActive = true;
    _callDuration = Duration.zero;
    _startCallTimer();
    notifyListeners();
  }

  void endConversation() {
    _currentPartner = null;
    _isCallActive = false;
    _callDuration = Duration.zero;
    _selectedConversationStarter = null;
    notifyListeners();
  }

  void toggleMute() {
    _isMuted = !_isMuted;
    notifyListeners();
  }

  void toggleVolume() {
    _isVolumeOn = !_isVolumeOn;
    notifyListeners();
  }

  void selectConversationStarter(String starterId) {
    _selectedConversationStarter = starterId;
    notifyListeners();
  }

  void _startCallTimer() {
    if (_isCallActive) {
      Future.delayed(const Duration(seconds: 1), () {
        if (_isCallActive) {
          _callDuration = Duration(seconds: _callDuration.inSeconds + 1);
          notifyListeners();
          _startCallTimer();
        }
      });
    }
  }

  String formatCallDuration() {
    final hours = _callDuration.inHours;
    final minutes = _callDuration.inMinutes % 60;
    final seconds = _callDuration.inSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  ConversationStarter? getConversationStarterById(String id) {
    try {
      return _conversationStarters.firstWhere((starter) => starter.id == id);
    } catch (e) {
      return null;
    }
  }
}
