import 'package:flutter/material.dart';

import '../core/app_export.dart';
import './custom_image_view.dart';

/**
 * CustomBottomBar - A flexible bottom navigation bar component
 * 
 * Supports variable number of tabs with icons and labels, customizable styling,
 * and navigation callbacks. Handles active/inactive states with different colors.
 * 
 * @param bottomBarItemList - List of bottom bar items to display
 * @param onChanged - Callback function when a tab is tapped, returns index
 * @param selectedIndex - Currently selected tab index (default: 0)
 * @param height - Height of the bottom bar (default: 75.h)
 * @param backgroundColor - Background color (default: appTheme.whiteCustom)
 * @param borderColor - Top border color (default: appTheme.colorFFEFF2)
 * @param activeTextColor - Color for active tab text (default: appTheme.colorFF1116)
 * @param inactiveTextColor - Color for inactive tab text (default: appTheme.colorFF607C)
 */
class CustomBottomBar extends StatelessWidget {
  CustomBottomBar({
    Key? key,
    required this.bottomBarItemList,
    required this.onChanged,
    this.selectedIndex,
    this.height,
    this.backgroundColor,
    this.borderColor,
    this.activeTextColor,
    this.inactiveTextColor,
  }) : super(key: key);

  /// List of bottom bar items with their properties
  final List<CustomBottomBarItem> bottomBarItemList;

  /// Current selected index of the bottom bar
  final int? selectedIndex;

  /// Callback function triggered when a bottom bar item is tapped
  final Function(int) onChanged;

  /// Height of the bottom bar container
  final double? height;

  /// Background color of the bottom bar
  final Color? backgroundColor;

  /// Color of the top border
  final Color? borderColor;

  /// Text color for active tab
  final Color? activeTextColor;

  /// Text color for inactive tabs
  final Color? inactiveTextColor;

  @override
  Widget build(BuildContext context) {
    final int currentSelectedIndex = selectedIndex ?? 0;
    final double containerHeight = height ?? 75.h;
    final Color bgColor = backgroundColor ?? appTheme.whiteCustom;
    final Color topBorderColor = borderColor ?? Color(0xFFEFF2F4);
    final Color activeColor = activeTextColor ?? Color(0xFF111616);
    final Color inactiveColor = inactiveTextColor ?? Color(0xFF607C89);

    return Container(
      height: containerHeight,
      width: double.infinity,
      decoration: BoxDecoration(
        color: bgColor,
        border: Border(
          top: BorderSide(
            color: topBorderColor,
            width: 1.h,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(bottomBarItemList.length, (index) {
          final isSelected = currentSelectedIndex == index;
          final item = bottomBarItemList[index];

          return InkWell(
            onTap: () {
              onChanged(index);
            },
            child: _buildBottomBarItem(
                item, isSelected, activeColor, inactiveColor),
          );
        }),
      ),
    );
  }

  Widget _buildBottomBarItem(
    CustomBottomBarItem item,
    bool isSelected,
    Color activeColor,
    Color inactiveColor,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        CustomImageView(
          imagePath: isSelected && item.activeIcon != null
              ? item.activeIcon!
              : item.icon,
          height: 24.h,
          width: 24.h,
        ),
        SizedBox(height: 4.h),
        Text(
          item.title,
          style: TextStyleHelper.instance.body12Medium
              .copyWith(color: isSelected ? activeColor : inactiveColor),
        ),
      ],
    );
  }
}

/// Item data model for custom bottom bar
class CustomBottomBarItem {
  CustomBottomBarItem({
    required this.icon,
    required this.title,
    required this.routeName,
    this.activeIcon,
  });

  /// Path to the default/inactive icon
  final String icon;

  /// Path to the active state icon (optional)
  final String? activeIcon;

  /// Title text shown below the icon
  final String title;

  /// Route name for navigation
  final String routeName;
}
