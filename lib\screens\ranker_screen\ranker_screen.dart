import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:learning_english_smart/routes/app_router.dart';
import '../../core/app_export.dart';
import '../../widgets/custom_bottom_navigation_bar.dart';

@RoutePage()
class Ranker_Screen extends StatefulWidget {
  const Ranker_Screen({super.key});

  @override
  State<Ranker_Screen> createState() => _Ranker_ScreenState();
}

class _Ranker_ScreenState extends State<Ranker_Screen> {
  int currentBottomIndex = 2; // Friends tab

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Xếp hạng'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () {
              // Add friend functionality
            },
            icon: const Icon(Icons.person_add),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search Bar
            _buildSearchBar(),
            SizedBox(height: 24.h),

            // Leaderboard Section
            Text(
              'Bảng xếp hạng tuần',
              style: TextStyle(
                fontSize: 20.fSize,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),
            _buildLeaderboard(),

            SizedBox(height: 24.h),

            // Friends List
            Text(
              'Danh sách bạn bè',
              style: TextStyle(
                fontSize: 20.fSize,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),

            _buildFriendItem(
                'Nguyễn Văn A', '1,250 XP', 'assets/images/avatar1.png', true),
            _buildFriendItem(
                'Trần Thị B', '980 XP', 'assets/images/avatar2.png', false),
            _buildFriendItem(
                'Lê Văn C', '750 XP', 'assets/images/avatar3.png', true),
            _buildFriendItem(
                'Phạm Thị D', '650 XP', 'assets/images/avatar4.png', false),
            _buildFriendItem(
                'Hoàng Văn E', '500 XP', 'assets/images/avatar5.png', true),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12.h),
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Tìm kiếm bạn bè...',
          border: InputBorder.none,
          icon: Icon(Icons.search, color: Colors.grey[600]),
          hintStyle: TextStyle(color: Colors.grey[600]),
        ),
      ),
    );
  }

  Widget _buildLeaderboard() {
    return Container(
      padding: EdgeInsets.all(16.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange[400]!, Colors.orange[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.h),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildPodiumItem('Trần B', '980', 2, Colors.grey[400]!),
              _buildPodiumItem('Nguyễn A', '1250', 1, Colors.amber),
              _buildPodiumItem('Lê C', '750', 3, Colors.brown[400]!),
            ],
          ),
          SizedBox(height: 16.h),
          Container(
            padding: EdgeInsets.all(12.h),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8.h),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.emoji_events, color: Colors.white, size: 20.h),
                SizedBox(width: 8.h),
                Text(
                  'Bạn đang ở vị trí #4 với 650 XP',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.fSize,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPodiumItem(String name, String xp, int rank, Color color) {
    return Column(
      children: [
        Container(
          width: 50.h,
          height: 50.h,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '$rank',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20.fSize,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          name,
          style: TextStyle(
            color: Colors.white,
            fontSize: 12.fSize,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          '$xp XP',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 10.fSize,
          ),
        ),
      ],
    );
  }

  Widget _buildFriendItem(
      String name, String xp, String avatarPath, bool isOnline) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.h),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Stack(
            children: [
              CircleAvatar(
                radius: 25.h,
                backgroundColor: Colors.blue[100],
                child: Icon(
                  Icons.person,
                  color: Colors.blue[600],
                  size: 30.h,
                ),
              ),
              if (isOnline)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 12.h,
                    height: 12.h,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(width: 16.h),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 16.fSize,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  xp,
                  style: TextStyle(
                    fontSize: 14.fSize,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // Challenge friend
            },
            icon: Icon(
              Icons.sports_esports,
              color: Colors.blue[600],
            ),
          ),
        ],
      ),
    );
  }
}
