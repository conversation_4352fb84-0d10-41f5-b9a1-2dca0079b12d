import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:learning_english_smart/pages/Learn_Page/models/learn_item_model.dart';
import 'package:learning_english_smart/routes/app_router.dart';
import '../../../core/app_export.dart';
import '../../../widgets/custom_bottom_bar.dart';

class LearnPrv extends ChangeNotifier {
  List<LearnItemModel> learnItemList = [];
  int selectedIndex = 1;

  @override
  void dispose() {
    super.dispose();
  }

  LearnPrv() {
    initialize();
  }

  void initialize() {
    learnItemList = [
      LearnItemModel(
        iconPath: ImageConstant.imgDepth3Frame0Gray90001,
        title: '<PERSON><PERSON><PERSON> học',
        description: 'Learn about the basics of sentence structure',
        learnType: 'baihoc',
      ),
      LearnItemModel(
        iconPath: ImageConstant.imgDepth3Frame0Gray90001,
        title: 'Tự vựng',
        description: 'Learn about the basics of sentence structure',
        learnType: 'vocabulary',
      ),
      LearnItemModel(
        iconPath: ImageConstant.imgDepth3Frame0Gray90001,
        title: 'Bài tập ngữ pháp',
        description: 'Understand the different types of verbs and their usage',
        learnType: 'exercise',
      ),
      // LearnItemModel(
      //   iconPath: ImageConstant.imgDepth3Frame0Gray90001,
      //   title: 'Articles and Agreement',
      //   description: 'Master the use of articles and their agreement',
      //   learnType: 'ai-chat',
      // ),
      LearnItemModel(
        iconPath: ImageConstant.imgDepth3Frame0Gray90001,
        title: 'Luyện nói',
        description: 'Learn how to use adjectives to describe nouns',
        learnType: 'speaking-practice',
      ),
      LearnItemModel(
        iconPath: ImageConstant.imgDepth3Frame0Gray90001,
        title: 'Sổ tay học tập',
        description: 'Understand the role of adverbs in modifying verbs',
        learnType: 'my-book',
      ),
      LearnItemModel(
        iconPath: ImageConstant.imgDepth3Frame0Gray90001,
        title: 'Ôn từ đã lưu',
        description: 'Learn how to connect words and phrases with conjunctions',
        learnType: 'saved-words',
      ),
      // LearnItemModel(
      //   iconPath: ImageConstant.imgDepth3Frame0Gray90001,
      //   title: 'Prepositions',
      //   description: 'Master the use of prepositions to show relationships',
      //   learnType: 'video-practice',
      // ),
      LearnItemModel(
        iconPath: ImageConstant.imgDepth3Frame0Gray90001,
        title: 'Trò chuyện',
        description: 'Understand how to form questions and negations',
        learnType: 'practice-partners',
      ),
    ];
  }

  void openLearn(BuildContext context, LearnItemModel learn) {
    PageRouteInfo targetRoute;

    switch (learn.learnType) {
      case 'baihoc':
        targetRoute = const CourseRoute();
        break;
      case 'vocabulary':
        targetRoute = const VocabularyRoute();
        break;
      case 'exercise':
        targetRoute = const ExerciseRoute();
        break;
      case 'ai-chat':
        targetRoute = const AiChatRoute();
        break;
      case 'speaking-practice':
        targetRoute = const SpeakingPracticeRoute();
        break;
      case 'my-book':
        targetRoute = const MyBookRoute();
        break;
      case 'saved-words':
        targetRoute = const SavedWordsRoute();
        break;
      // case 'video-practice':
      //   targetRoute =
      //       const SpeakingPracticeRoute(); // Tạm thời dùng SpeakingPractice
      // break;
      case 'practice-partners':
        targetRoute = const Ranker_Route();
        break;
      default:
        targetRoute = const VocabularyRoute();
    }

    context.router.push(targetRoute);
  }

  void updateSelectedIndex(int index) {
    selectedIndex = index;
    notifyListeners();
  }
}
