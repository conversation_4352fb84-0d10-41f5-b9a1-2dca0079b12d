import 'package:flutter/material.dart';
import 'package:learning_english_smart/pages/BanBe_Page/BanBe_Page.dart';
import 'package:learning_english_smart/pages/Learn_Page/learn_page.dart';
import '../../../core/app_export.dart';
import '../../Home_Page/home_page.dart';
import '../../Account_Page/account_page.dart';

Widget buildPage(int index) {
  List<Widget> widgets = [
    const HomePage(),
    const LearnPage(),
    const BanBePage(),
    const AccountPage(),
  ];

  return widgets[index];
}

var bottomTabs = [
  BottomNavigationBarItem(
    label: "Trang chủ",
    icon: Icon(Icons.home_outlined, size: 24.h),
    activeIcon: Icon(Icons.home, size: 24.h),
  ),
  BottomNavigationBarItem(
    label: "Học tập",
    icon: Icon(Icons.book_outlined, size: 24.h),
    activeIcon: Icon(Icons.book, size: 24.h),
  ),
  BottomNavigationBarItem(
    label: "Bạn bè",
    icon: Icon(Icons.people_outline, size: 24.h),
    activeIcon: Icon(Icons.people, size: 24.h),
  ),
  BottomNavigationBarItem(
    label: "Tài khoản",
    icon: Icon(Icons.person_outline, size: 24.h),
    activeIcon: Icon(Icons.person, size: 24.h),
  ),
];
