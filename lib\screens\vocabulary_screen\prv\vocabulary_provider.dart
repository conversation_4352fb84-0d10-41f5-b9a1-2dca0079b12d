import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:learning_english_smart/screens/saved_words_screen/model/saved_word.dart';
import 'package:learning_english_smart/screens/vocabulary_screen/model/vocabulary_item.dart';
import 'package:learning_english_smart/screens/vocabulary_screen/model/memory_status.dart';
import 'package:learning_english_smart/services/vocabulary_service.dart';
import 'package:learning_english_smart/services/vocabulary_cache_service.dart';

enum LearningMode { study, multipleChoice, speaking }

enum ExerciseType { study, translation, listening, wordOrder, completion }

class VocabularyProvider with ChangeNotifier {
  LearningMode _currentMode = LearningMode.study;
  int _currentIndex = 0;
  String? _selectedAnswer;
  bool _isRecording = false;

  ExerciseType _currentExerciseType = ExerciseType.study;
  int _currentExerciseIndex = 0;
  final List<String> _selectedWords = [];
  bool _isExerciseCompleted = false;
  String _userAnswer = '';

  // Add new properties for the new screens
  List<SavedWord> _savedWords = [];
  String _selectedBookSection = 'vocabulary';
  double _speakingProgress = 0.0;

  // Vocabulary service instance
  final VocabularyService _vocabularyService = VocabularyService();
  final VocabularyCacheService _cacheService = VocabularyCacheService();

  // Vocabulary data
  List<VocabularyItem> _vocabularyItems = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _selectedLevel = 'All'; // All, A1, A2, B1, B2, C1, C2
  String _searchQuery = '';

  // Progress tracking for each level
  final Map<String, int> _levelProgress = {
    'A1': 0,
    'A2': 0,
    'B1': 0,
    'B2': 0,
    'C1': 0,
    'C2': 0,
  };

  // Combo tracking
  int _currentCombo = 0;

  // Memory status tracking - Map<wordId, MemoryStatus>
  final Map<String, MemoryStatus> _wordMemoryStatus = {};

  final List<SavedWord> _defaultSavedWords = [
    SavedWord(
      word: 'as many as',
      pronunciation: '/æz ˈmeni æz/',
      meaning: 'nhiều như',
      example: 'You can take as many as you want.',
      savedDate: DateTime.now().subtract(const Duration(days: 1)),
      reviewCount: 3,
    ),
    SavedWord(
      word: 'City break',
      pronunciation: '/ˈsɪti breɪk/',
      meaning: 'kỳ nghỉ ngắn ở thành phố',
      example: 'We went on a city break to Paris.',
      savedDate: DateTime.now().subtract(const Duration(days: 2)),
      reviewCount: 1,
    ),
    SavedWord(
      word: 'Cosmopolitan',
      pronunciation: '/ˌkɒzməˈpɒlɪtən/',
      meaning: 'thuộc về thế giới',
      example: 'New York is a cosmopolitan city.',
      savedDate: DateTime.now().subtract(const Duration(days: 3)),
      reviewCount: 2,
    ),
    SavedWord(
      word: 'Crowded',
      pronunciation: '/ˈkraʊdɪd/',
      meaning: 'đông đúc',
      example: 'The street was very crowded.',
      savedDate: DateTime.now().subtract(const Duration(days: 4)),
      reviewCount: 5,
    ),
    SavedWord(
      word: 'Embassy',
      pronunciation: '/ˈembəsi/',
      meaning: 'đại sứ quán',
      example: 'I need to visit the embassy.',
      savedDate: DateTime.now().subtract(const Duration(days: 5)),
      reviewCount: 1,
    ),
    SavedWord(
      word: 'Getaway',
      pronunciation: '/ˈɡetəweɪ/',
      meaning: 'chuyến đi nghỉ',
      example: 'We planned a weekend getaway.',
      savedDate: DateTime.now().subtract(const Duration(days: 6)),
      reviewCount: 2,
    ),
    SavedWord(
      word: 'Disappointing',
      pronunciation: '/ˌdɪsəˈpɔɪntɪŋ/',
      meaning: 'thất vọng',
      example: 'The movie was disappointing.',
      savedDate: DateTime.now().subtract(const Duration(days: 7)),
      reviewCount: 3,
    ),
    SavedWord(
      word: 'Jaw-dropping',
      pronunciation: '/ˈdʒɔː drɒpɪŋ/',
      meaning: 'đáng kinh ngạc',
      example: 'The view was jaw-dropping.',
      savedDate: DateTime.now().subtract(const Duration(days: 8)),
      reviewCount: 1,
    ),
    SavedWord(
      word: 'Lively',
      pronunciation: '/ˈlaɪvli/',
      meaning: 'sống động',
      example: 'The party was very lively.',
      savedDate: DateTime.now().subtract(const Duration(days: 9)),
      reviewCount: 4,
    ),
  ];

  // Getters
  LearningMode get currentMode => _currentMode;
  int get currentIndex => _currentIndex;
  String? get selectedAnswer => _selectedAnswer;
  bool get isRecording => _isRecording;
  VocabularyItem get currentItem => _vocabularyItems.isNotEmpty
      ? _vocabularyItems[_currentIndex]
      : VocabularyItem(
          word: 'Loading...',
          pronunciation: '',
          meaning: '',
          example: '',
          imageUrl: '',
          choices: [],
          exercises: []);
  List<VocabularyItem> get vocabularyItems => _vocabularyItems;
  ExerciseType get currentExerciseType => _currentExerciseType;
  int get currentExerciseIndex => _currentExerciseIndex;
  List<String> get selectedWords => _selectedWords;
  bool get isExerciseCompleted => _isExerciseCompleted;
  String get userAnswer => _userAnswer;
  List<SavedWord> get savedWords =>
      _savedWords.isEmpty ? _defaultSavedWords : _savedWords;
  String get selectedBookSection => _selectedBookSection;
  double get speakingProgress => _speakingProgress;

  // New getters for vocabulary management
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get selectedLevel => _selectedLevel;
  String get searchQuery => _searchQuery;
  bool get hasVocabularyData => _vocabularyItems.isNotEmpty;

  // Progress getters
  int getCurrentProgress() => _levelProgress[_selectedLevel] ?? 0;
  int getTotalForLevel() => getVocabularyStats()[_selectedLevel] ?? 0;
  int getProgressForLevel(String level) => _levelProgress[level] ?? 0;

  // Combo getters
  int get currentCombo => _currentCombo;

  // Memory status getters
  MemoryStatus getWordMemoryStatus(String word) {
    return _wordMemoryStatus[word] ?? MemoryStatus.unknown;
  }

  MemoryStatus getCurrentWordMemoryStatus() {
    if (_vocabularyItems.isEmpty) return MemoryStatus.unknown;
    return getWordMemoryStatus(_vocabularyItems[_currentIndex].word);
  }

  VocabularyProvider() {
    _savedWords = _defaultSavedWords;
    _initializeVocabulary();
  }

  // Initialize vocabulary data
  Future<void> _initializeVocabulary() async {
    await _loadProgressFromStorage();
    await loadVocabularyData();
  }

  void setMode(LearningMode mode) {
    _currentMode = mode;
    notifyListeners();
  }

  void setExerciseType(ExerciseType type) {
    _currentExerciseType = type;
    notifyListeners();
  }

  void setBookSection(String section) {
    _selectedBookSection = section;
    notifyListeners();
  }

  void setSpeakingProgress(double progress) {
    _speakingProgress = progress;
    notifyListeners();
  }

  void addSelectedWord(String word) {
    _selectedWords.add(word);
    notifyListeners();
  }

  void removeSelectedWord(String word) {
    _selectedWords.remove(word);
    notifyListeners();
  }

  void clearSelectedWords() {
    _selectedWords.clear();
    notifyListeners();
  }

  void setUserAnswer(String answer) {
    _userAnswer = answer;
    notifyListeners();
  }

  void completeExercise() {
    _isExerciseCompleted = true;
    notifyListeners();
  }

  void nextExercise() {
    _currentExerciseIndex++;
    _selectedWords.clear();
    _userAnswer = '';
    _isExerciseCompleted = false;
    notifyListeners();
  }

  void selectAnswer(String answer) {
    _selectedAnswer = answer;
    notifyListeners();
  }

  void nextItem() {
    if (_vocabularyItems.isEmpty) return;

    if (_currentIndex < _vocabularyItems.length - 1) {
      _currentIndex++;
      _selectedAnswer = null;

      // Update progress for current level
      if (_selectedLevel != 'All') {
        _levelProgress[_selectedLevel] = _currentIndex;
        _saveProgressToStorage(); // Save to storage
      }

      notifyListeners();
    }
  }

  void previousItem() {
    if (_vocabularyItems.isEmpty) return;

    if (_currentIndex > 0) {
      _currentIndex--;
      _selectedAnswer = null;

      // Update progress for current level
      if (_selectedLevel != 'All') {
        _levelProgress[_selectedLevel] = _currentIndex;
        _saveProgressToStorage(); // Save to storage
      }

      notifyListeners();
    }
  }

  // Set selected level and refresh data
  Future<void> setSelectedLevel(String level) async {
    // Save current progress for current level
    if (_vocabularyItems.isNotEmpty && _selectedLevel != 'All') {
      _levelProgress[_selectedLevel] = _currentIndex;
      print('💾 Saved progress for $_selectedLevel: $_currentIndex');
    }

    // Switch to new level
    _selectedLevel = level;

    // Load data for new level
    await loadVocabularyData();

    // Restore progress for new level (or start from beginning)
    if (level != 'All') {
      _currentIndex = _levelProgress[level] ?? 0;
      print('📖 Restored progress for $level: $_currentIndex');
    } else {
      _currentIndex = 0;
    }

    // Save to storage immediately
    await _saveProgressToStorage();

    notifyListeners();
  }

  void toggleRecording() {
    _isRecording = !_isRecording;
    notifyListeners();
  }

  // Combo management
  void incrementCombo() {
    _currentCombo++;
    notifyListeners();
  }

  void resetCombo() {
    _currentCombo = 0;
    notifyListeners();
  }

  // Memory status management
  void setWordMemoryStatus(String word, MemoryStatus status) {
    _wordMemoryStatus[word] = status;
    _saveMemoryStatusToStorage();
    notifyListeners();
  }

  void setCurrentWordMemoryStatus(MemoryStatus status) {
    if (_vocabularyItems.isNotEmpty) {
      setWordMemoryStatus(_vocabularyItems[_currentIndex].word, status);
    }
  }

  // Load progress from SharedPreferences
  Future<void> _loadProgressFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load level progress
      for (final level in _levelProgress.keys) {
        final progress = prefs.getInt('progress_$level') ?? 0;
        _levelProgress[level] = progress;
      }

      // Load selected level
      _selectedLevel = prefs.getString('selected_level') ?? 'A1';

      // Load current index for selected level
      _currentIndex = prefs.getInt('current_index') ?? 0;

      // Load memory status
      await _loadMemoryStatusFromStorage();

      print(
          '📱 Loaded from storage - Level: $_selectedLevel, Index: $_currentIndex, Progress: $_levelProgress');
    } catch (e) {
      print('❌ Error loading progress: $e');
    }
  }

  // Save progress to SharedPreferences
  Future<void> _saveProgressToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save level progress
      for (final entry in _levelProgress.entries) {
        await prefs.setInt('progress_${entry.key}', entry.value);
      }

      // Save selected level
      await prefs.setString('selected_level', _selectedLevel);

      // Save current index
      await prefs.setInt('current_index', _currentIndex);

      print(
          '💾 Saved to storage - Level: $_selectedLevel, Index: $_currentIndex, Progress: $_levelProgress');
    } catch (e) {
      print('❌ Error saving progress: $e');
    }
  }

  void resetProgress() {
    _currentIndex = 0;
    _selectedAnswer = null;
    _isRecording = false;
    notifyListeners();
  }

  // Load memory status from SharedPreferences
  Future<void> _loadMemoryStatusFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('memory_'));

      for (final key in keys) {
        final word = key.substring(7); // Remove 'memory_' prefix
        final statusKey = prefs.getString(key);
        if (statusKey != null) {
          _wordMemoryStatus[word] = MemoryStatusExtension.fromKey(statusKey);
        }
      }

      print('📱 Loaded ${_wordMemoryStatus.length} memory statuses');
    } catch (e) {
      print('❌ Error loading memory status: $e');
    }
  }

  // Save memory status to SharedPreferences
  Future<void> _saveMemoryStatusToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      for (final entry in _wordMemoryStatus.entries) {
        await prefs.setString('memory_${entry.key}', entry.value.key);
      }

      print('💾 Saved ${_wordMemoryStatus.length} memory statuses');
    } catch (e) {
      print('❌ Error saving memory status: $e');
    }
  }

  void addToSavedWords(SavedWord word) {
    _savedWords.add(word);
    notifyListeners();
  }

  void removeFromSavedWords(SavedWord word) {
    _savedWords.remove(word);
    notifyListeners();
  }

  // ===== NEW VOCABULARY MANAGEMENT METHODS =====

  // Load vocabulary data from cache (instant) or Firebase (if needed)
  Future<void> loadVocabularyData() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      print('🚀 Loading vocabulary data from cache...');

      // Check if cache service is initialized and has data
      if (_cacheService.isInitialized && _cacheService.hasCachedData) {
        print('⚡ Using cached vocabulary data - instant load!');

        // Load từ cache - instant!
        final cachedWords = _cacheService.getAllVocabularies();

        // Set vào vocabulary service để tương thích với code hiện tại
        _vocabularyService.setCachedWords(cachedWords);

        print('✅ Loaded ${cachedWords.length} vocabularies from cache');
      } else {
        print('📡 Cache not ready, loading from Firebase...');

        // Fallback: load từ Firebase nếu cache chưa sẵn sàng
        await _vocabularyService.initializeFirebase();
        await _vocabularyService.loadVocabulariesFromFirebase();

        print(
            '✅ Loaded ${_vocabularyService.allWords.length} vocabularies from Firebase');
      }

      // Get vocabulary items with current filters
      _refreshVocabularyItems();

      print(
          '🔍 After refresh: ${_vocabularyItems.length} vocabulary items for level $_selectedLevel');

      // Nếu không có dữ liệu sau khi load từ cache/Firebase
      if (_vocabularyItems.isEmpty) {
        print('⚠️ Không có dữ liệu từ vựng cho level $_selectedLevel');
        _errorMessage = 'Không có dữ liệu từ vựng cho level $_selectedLevel';
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Lỗi khi tải dữ liệu từ vựng: $e';

      // Fallback: Thông báo lỗi cho user
      print('❌ Lỗi load vocabulary: $e');

      notifyListeners();
    }
  }

  // Refresh vocabulary items based on current filters
  void _refreshVocabularyItems() {
    final level = _selectedLevel == 'All' ? null : _selectedLevel;
    final search = _searchQuery.isEmpty ? null : _searchQuery;

    _vocabularyItems = _vocabularyService.getVocabularyItems(
      level: level,
      search: search,
    );

    // Reset current index if out of bounds
    if (_currentIndex >= _vocabularyItems.length) {
      _currentIndex = 0;
    }
  }

  // Set level filter
  void setLevelFilter(String level) {
    _selectedLevel = level;
    _refreshVocabularyItems();
    notifyListeners();
  }

  // Set search query
  void setSearchQuery(String query) {
    _searchQuery = query;
    _refreshVocabularyItems();
    notifyListeners();
  }

  // Get available levels
  List<String> getAvailableLevels() {
    if (!_vocabularyService.isLoaded) return ['All'];

    final levels =
        _vocabularyService.allWords.map((word) => word.level).toSet().toList();
    levels.sort();
    return ['All', ...levels];
  }

  // Get vocabulary statistics (Firebase version)
  Future<Map<String, int>> getVocabularyStatsAsync() async {
    try {
      return await _vocabularyService.getFirebaseStatistics();
    } catch (e) {
      return _vocabularyService.getStatistics(); // Fallback to local
    }
  }

  // Get vocabulary statistics (sync version for UI)
  Map<String, int> getVocabularyStats() {
    // Ưu tiên cache trước
    if (_cacheService.isInitialized && _cacheService.hasCachedData) {
      return _cacheService.getStatistics();
    }

    // Fallback sang vocabulary service
    final stats = _vocabularyService.getStatistics();

    // Nếu không có dữ liệu, trả về empty stats
    if (stats.isEmpty || stats.values.every((count) => count == 0)) {
      return {
        'A1': 0,
        'A2': 0,
        'B1': 0,
        'B2': 0,
        'C1': 0,
        'C2': 0,
        'total': 0,
      };
    }

    return stats;
  }

  // Get total words for selected level
  int getTotalWordsForLevel() {
    final stats = getVocabularyStats();
    return stats[_selectedLevel] ?? 0;
  }

  // Reload vocabulary data
  Future<void> reloadVocabularyData() async {
    _vocabularyService.reset();
    await loadVocabularyData();
  }
}
